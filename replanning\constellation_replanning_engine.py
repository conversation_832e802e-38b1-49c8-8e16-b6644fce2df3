"""
星座重规划引擎 - 集成现有GPNConstellation架构

基于现有GPNConstellation模型，实现动态重规划功能
支持多层次触发、MAML快速适应、约束处理等核心功能
"""

import time
import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging

# 导入现有模块
from constellation_smp.gpn_constellation import GPNConstellation
from constellation_smp.constellation_smp import reward as constellation_smp_reward

# 导入重规划模块
from .maml_replanner import ConstellationMAMLReplanner
from .trigger_system import ConstellationReplanningTrigger
from .state_monitor import ConstellationStateMonitor
from .adaptive_reward import AdaptiveRewardManager
from .constraint_handler import ConstraintHandler


class ConstellationReplanningEngine:
    """
    星座重规划引擎 - 集成现有GPNConstellation架构
    
    核心功能:
    1. 集成现有GPNConstellation模型
    2. 实现多层次触发机制
    3. MAML快速适应
    4. 约束处理和验证
    5. 性能监控和反馈
    """
    
    def __init__(self, gpn_constellation_model: GPNConstellation, config: Dict[str, Any]):
        """
        初始化重规划引擎
        
        Args:
            gpn_constellation_model: 现有的GPNConstellation模型
            config: 配置参数字典
        """
        self.base_planner = gpn_constellation_model
        self.config = config
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 核心组件初始化
        self._initialize_components()
        
        # 状态管理
        self.current_state = None
        self.planning_history = []
        self.performance_metrics = {}
        self.replanning_count = 0
        
        # 性能统计
        self.response_times = []
        self.success_rate = 0.0
        
        self.logger.info("ConstellationReplanningEngine initialized successfully")
    
    def _initialize_components(self):
        """初始化各个组件"""
        try:
            # MAML学习器
            self.maml_learner = ConstellationMAMLReplanner(
                self.base_planner,
                inner_lr=self.config.get('maml_inner_lr', 0.01),
                outer_lr=self.config.get('maml_outer_lr', 0.001)
            )
            
            # 触发管理器
            self.trigger_manager = ConstellationReplanningTrigger(
                self.base_planner, 
                self.config
            )
            
            # 状态监控器
            self.state_monitor = ConstellationStateMonitor(
                num_satellites=self.base_planner.num_satellites,
                constellation_mode=self.base_planner.constellation_mode
            )
            
            # 自适应奖励管理器
            self.adaptive_optimizer = AdaptiveRewardManager(
                constellation_mode=self.base_planner.constellation_mode
            )
            
            # 约束处理器
            self.constraint_handler = ConstraintHandler(self.config)
            
            self.logger.info("All components initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize components: {e}")
            raise
    
    def execute_replanning_cycle(self, static_data: torch.Tensor, 
                                dynamic_data: torch.Tensor) -> Dict[str, Any]:
        """
        执行完整的重规划周期
        
        Args:
            static_data: 静态任务数据 (batch_size, static_size, seq_len)
            dynamic_data: 动态状态数据 (batch_size, dynamic_size, seq_len, num_satellites)
            
        Returns:
            Dict: 规划结果字典
        """
        try:
            # 1. 状态更新与监控
            current_state = self.update_system_state(static_data, dynamic_data)
            
            # 2. 触发条件检查
            trigger_signals = self.trigger_manager.check_replanning_need(
                current_state, dynamic_data
            )
            
            if not trigger_signals:
                # 无需重规划，返回当前规划
                return self.get_current_plan(static_data, dynamic_data)
            
            # 3. 执行重规划流程
            self.logger.info(f"Replanning triggered: {list(trigger_signals.keys())}")
            return self.execute_replanning(current_state, trigger_signals)
            
        except Exception as e:
            self.logger.error(f"Replanning cycle failed: {e}")
            return self.generate_fallback_plan(static_data, dynamic_data)
    
    def execute_replanning(self, current_state: Dict[str, Any], 
                          trigger_signals: Dict[str, float]) -> Dict[str, Any]:
        """
        执行重规划流程
        
        Args:
            current_state: 当前系统状态
            trigger_signals: 触发信号字典
            
        Returns:
            Dict: 重规划结果
        """
        start_time = time.time()
        
        try:
            # 1. 分析触发原因
            trigger_analysis = self.analyze_triggers(trigger_signals)
            
            # 2. MAML快速适应（修复：支持训练期间的重规划学习）
            if self.base_planner.training:
                # 修复：训练期间使用简化的重规划策略
                try:
                    # 检查是否应该进行重规划学习（概率性执行，避免过度干扰主训练）
                    import random
                    if random.random() < 0.3:  # 30%概率执行重规划学习
                        self.logger.debug("Executing simplified replanning during training")
                        adapted_params = self.maml_learner.fast_adapt(
                            self.generate_adaptation_data(current_state, trigger_analysis),
                            adaptation_steps=2  # 训练时减少适应步数
                        )
                        # 应用适应后的参数（仅用于当前批次）
                        self.apply_adapted_parameters_temporarily(adapted_params)
                    else:
                        self.logger.debug("Skipping replanning during training (probabilistic)")
                except Exception as e:
                    self.logger.warning(f"Training-time MAML adaptation failed: {e}")
                    # 继续使用原始参数
            else:
                # 推理期间使用完整的重规划
                try:
                    adapted_params = self.maml_learner.fast_adapt(
                        self.generate_adaptation_data(current_state, trigger_analysis),
                        adaptation_steps=self.config.get('adaptation_steps', 5)
                    )

                    # 3. 应用适应后的参数
                    self.apply_adapted_parameters(adapted_params)
                except Exception as e:
                    self.logger.warning(f"MAML adaptation failed: {e}")
                    # 继续使用原始参数
            
            # 4. 生成新规划
            new_plan = self.generate_new_plan(current_state)
            
            # 5. 约束验证与修复
            validated_plan = self.validate_and_repair_plan(new_plan)
            
            # 6. 性能评估与反馈
            self.evaluate_and_record_performance(validated_plan, current_state)
            
            # 记录性能指标
            replanning_time = time.time() - start_time
            self.response_times.append(replanning_time)
            self.replanning_count += 1
            
            self.log_replanning_event(trigger_signals, replanning_time, validated_plan)
            
            return validated_plan
            
        except Exception as e:
            self.logger.error(f"Replanning execution failed: {e}")
            return self.generate_fallback_plan(
                current_state['static'], 
                current_state['dynamic']
            )
    
    def update_system_state(self, static_data: torch.Tensor, 
                           dynamic_data: torch.Tensor) -> Dict[str, Any]:
        """更新系统状态"""
        current_state = {
            'static': static_data,
            'dynamic': dynamic_data,
            'timestamp': time.time(),
            'replanning_count': self.replanning_count
        }
        
        # 使用状态监控器检测变化
        state_changes = self.state_monitor.detect_changes(dynamic_data)
        current_state['changes'] = state_changes
        
        self.current_state = current_state
        return current_state
    
    def get_current_plan(self, static_data: torch.Tensor, 
                        dynamic_data: torch.Tensor) -> Dict[str, Any]:
        """获取当前规划（无需重规划时）"""
        with torch.no_grad():
            tour_indices, satellite_indices, tour_logp, sat_logp = \
                self.base_planner(static_data, dynamic_data)
        
        # 计算奖励
        reward_value, revenue_rate, distance, memory, power = constellation_smp_reward(
            static_data, tour_indices, satellite_indices,
            self.base_planner.constellation_mode
        )
        
        return {
            'tour_indices': tour_indices,
            'satellite_indices': satellite_indices,
            'tour_logp': tour_logp,
            'sat_logp': sat_logp,
            'reward': reward_value,
            'revenue_rate': revenue_rate,
            'replanning_triggered': False,
            'timestamp': time.time()
        }
    
    def analyze_triggers(self, trigger_signals: Dict[str, float]) -> Dict[str, Any]:
        """分析触发信号"""
        analysis = {
            'primary_trigger': max(trigger_signals.items(), key=lambda x: x[1]),
            'trigger_count': len(trigger_signals),
            'severity': max(trigger_signals.values()),
            'trigger_types': list(trigger_signals.keys())
        }
        return analysis
    
    def generate_adaptation_data(self, current_state: Dict[str, Any],
                               trigger_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成适应数据"""
        try:
            static = current_state['static']
            dynamic = current_state['dynamic']

            # 生成简单的适应数据用于测试
            # 在实际应用中，这里应该根据触发原因生成更智能的适应样本
            batch_size = static.size(0)
            seq_len = static.size(2)

            # 生成一些随机的目标动作作为适应目标
            device = static.device
            target_tour = torch.randint(0, seq_len, (batch_size, seq_len // 2)).to(device)
            target_satellites = torch.randint(0, self.config.get('num_satellites', 3), (batch_size, seq_len // 2)).to(device)

            adaptation_data = [
                {
                    'static': static,
                    'dynamic': dynamic,
                    'target_actions': target_tour,
                    'target_satellites': target_satellites
                }
            ]

            return adaptation_data

        except Exception as e:
            self.logger.error(f"Failed to generate adaptation data: {e}")
            return []
    
    def apply_adapted_parameters(self, adapted_params: Dict[str, torch.Tensor]):
        """应用适应后的参数（永久性）"""
        try:
            for name, param in self.base_planner.named_parameters():
                if name in adapted_params:
                    param.data = adapted_params[name].clone()
            self.logger.debug("Applied adapted parameters")
        except Exception as e:
            self.logger.error(f"Failed to apply adapted parameters: {e}")

    def apply_adapted_parameters_temporarily(self, adapted_params: Dict[str, torch.Tensor]):
        """临时应用适应后的参数（仅用于训练期间）"""
        try:
            # 保存原始参数
            original_params = {}
            for name, param in self.base_planner.named_parameters():
                if name in adapted_params:
                    original_params[name] = param.data.clone()
                    param.data = adapted_params[name].clone()

            # 存储原始参数以便后续恢复
            self._temp_original_params = original_params
            self.logger.debug("Temporarily applied adapted parameters")
        except Exception as e:
            self.logger.error(f"Failed to temporarily apply adapted parameters: {e}")

    def restore_original_parameters(self):
        """恢复原始参数（训练期间使用）"""
        try:
            if hasattr(self, '_temp_original_params'):
                for name, param in self.base_planner.named_parameters():
                    if name in self._temp_original_params:
                        param.data = self._temp_original_params[name]
                delattr(self, '_temp_original_params')
                self.logger.debug("Restored original parameters")
        except Exception as e:
            self.logger.error(f"Failed to restore original parameters: {e}")
    
    def generate_new_plan(self, current_state: Dict[str, Any]) -> Dict[str, Any]:
        """生成新的规划"""
        static = current_state['static']
        dynamic = current_state['dynamic']
        
        # 使用适应后的模型生成新规划
        with torch.no_grad():
            tour_indices, satellite_indices, tour_logp, sat_logp = \
                self.base_planner(static, dynamic)
        
        # 计算自适应奖励
        adaptive_reward, reward_components, weights = \
            self.adaptive_optimizer.compute_adaptive_reward(
                static, tour_indices, satellite_indices,
                self.base_planner.constellation_mode, 
                current_state.get('situation')
            )
        
        return {
            'tour_indices': tour_indices,
            'satellite_indices': satellite_indices,
            'tour_logp': tour_logp,
            'sat_logp': sat_logp,
            'reward': adaptive_reward,
            'reward_components': reward_components,
            'weights': weights,
            'timestamp': time.time(),
            'replanning_triggered': True
        }
    
    def validate_and_repair_plan(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """验证和修复规划"""
        # 使用约束处理器验证
        is_valid, penalty = self.constraint_handler.validate_solution(plan)
        
        if not is_valid:
            # 修复不可行解
            plan = self.constraint_handler.repair_solution(plan)
            plan['repaired'] = True
        else:
            plan['repaired'] = False
        
        plan['constraint_penalty'] = penalty
        return plan
    
    def evaluate_and_record_performance(self, plan: Dict[str, Any], 
                                      current_state: Dict[str, Any]):
        """评估和记录性能"""
        # 记录规划历史
        self.planning_history.append({
            'timestamp': time.time(),
            'plan': plan,
            'state': current_state,
            'performance': plan.get('reward', 0)
        })
        
        # 更新性能指标
        if len(self.planning_history) > 1:
            recent_performance = [h['performance'] for h in self.planning_history[-10:]]
            self.performance_metrics['recent_avg'] = np.mean(recent_performance)
            self.performance_metrics['recent_std'] = np.std(recent_performance)
    
    def generate_fallback_plan(self, static_data: torch.Tensor, 
                             dynamic_data: torch.Tensor) -> Dict[str, Any]:
        """生成备用规划（重规划失败时）"""
        self.logger.warning("Using fallback plan due to replanning failure")
        
        # 使用原始模型生成安全的备用方案
        with torch.no_grad():
            tour_indices, satellite_indices, tour_logp, sat_logp = \
                self.base_planner(static_data, dynamic_data)
        
        reward, revenue_rate, distance, memory, power = constellation_smp_reward(
            static_data, tour_indices, satellite_indices,
            self.base_planner.constellation_mode
        )
        
        return {
            'tour_indices': tour_indices,
            'satellite_indices': satellite_indices,
            'tour_logp': tour_logp,
            'sat_logp': sat_logp,
            'reward': reward,
            'revenue_rate': revenue_rate,
            'fallback_plan': True,
            'timestamp': time.time()
        }
    
    def log_replanning_event(self, trigger_signals: Dict[str, float], 
                           response_time: float, plan: Dict[str, Any]):
        """记录重规划事件"""
        self.logger.info(
            f"Replanning completed - "
            f"Triggers: {trigger_signals}, "
            f"Response time: {response_time:.3f}s, "
            f"Reward: {plan.get('reward', 'N/A')}"
        )
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.response_times:
            return {'status': 'No replanning events recorded'}
        
        return {
            'total_replanning_events': self.replanning_count,
            'avg_response_time': np.mean(self.response_times),
            'max_response_time': np.max(self.response_times),
            'min_response_time': np.min(self.response_times),
            'success_rate': self.success_rate,
            'recent_performance': self.performance_metrics.get('recent_avg', 0)
        }
