#!/usr/bin/env python3
"""
调试奖励函数调用问题
"""

import torch
import traceback
from constellation_smp.gpn_constellation import GPNConstellation
from constellation_smp.constellation_smp import reward

def test_reward_function():
    """测试奖励函数调用"""
    print("测试奖励函数调用...")
    
    try:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {device}")
        
        # 创建测试数据
        batch_size = 2
        seq_len = 5
        static = torch.randn(batch_size, 9, seq_len).to(device)
        dynamic = torch.randn(batch_size, 7, seq_len, 3).to(device)
        
        # 创建测试模型
        model = GPNConstellation(
            static_size=9, dynamic_size=7, hidden_size=128, 
            num_satellites=3, constellation_mode='hybrid'
        ).to(device)
        
        print("模型创建成功")
        
        # 测试模型前向传播
        with torch.no_grad():
            model.eval()
            outputs = model(static, dynamic)
            print(f"模型输出数量: {len(outputs)}")
            print(f"输出类型: {[type(x) for x in outputs]}")
            
            tour_indices, satellite_indices, tour_logp, sat_logp = outputs
            print(f"tour_indices shape: {tour_indices.shape}")
            print(f"satellite_indices shape: {satellite_indices.shape}")
            print(f"tour_logp shape: {tour_logp.shape if tour_logp is not None else 'None'}")
            print(f"sat_logp shape: {sat_logp.shape if sat_logp is not None else 'None'}")
        
        # 测试奖励函数调用
        print("\n测试奖励函数调用...")
        
        # 检查reward函数是否可调用
        print(f"reward函数类型: {type(reward)}")
        print(f"reward函数是否可调用: {callable(reward)}")
        
        if callable(reward):
            try:
                reward_result = reward(static, tour_indices, satellite_indices, 'hybrid')
                print(f"奖励函数调用成功")
                print(f"返回值数量: {len(reward_result)}")
                print(f"返回值类型: {[type(x) for x in reward_result]}")
                
                reward_value, revenue_rate, distance, memory, power = reward_result
                print(f"reward shape: {reward_value.shape}")
                print(f"revenue_rate shape: {revenue_rate.shape}")
                print(f"distance shape: {distance.shape}")
                print(f"memory shape: {memory.shape}")
                print(f"power shape: {power.shape}")
                
            except Exception as e:
                print(f"奖励函数调用失败: {e}")
                traceback.print_exc()
        else:
            print("❌ reward函数不可调用")
            
    except Exception as e:
        print(f"测试失败: {e}")
        traceback.print_exc()

def test_import():
    """测试导入"""
    print("测试模块导入...")
    
    try:
        from constellation_smp.constellation_smp import reward
        print(f"✅ 成功导入reward函数: {reward}")
        print(f"函数类型: {type(reward)}")
        print(f"是否可调用: {callable(reward)}")
        
        # 检查函数签名
        import inspect
        sig = inspect.signature(reward)
        print(f"函数签名: {sig}")
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    test_import()
    print("\n" + "="*50 + "\n")
    test_reward_function()
