constellation_smp: 100
model: gpn
rnn: indrnn
hidden_size: 256
batch_size: 32
seed: 12346
train-size: 100000
valid-size: 10000
epochs: 3
lr: 0.0001
memory_total: 0.3
power_total: 5
dropout: 0.1
actor_lr: 5e-05
critic_lr: 0.0001
num_satellites: 3
constellation_mode: hybrid
use_transformer: True
transformer_layers: 2
transformer_heads: 4
transformer_d_model: 256
transformer_d_ff: 512
transformer_dropout: 0.1
transformer_activation: gelu
transformer_integration_mode: parallel
verbose: True
2025_08_20_21_51_52
启用Transformer增强模块，配置: {'d_model': 256, 'num_heads': 4, 'd_ff': 512, 'num_layers': 2, 'max_len': 5000, 'dropout': 0.1, 'activation': 'gelu'}

开始训练 Epoch 1/3
Epoch 1, Batch 10/3125, loss: 261.276, reward: 12.271, critic_reward: 10.536, revenue_rate: 0.3138, distance: 4.8244, memory: -0.0204, power: 0.1453, lr: 0.000050, took: 66.003s
Epoch 1, Batch 20/3125, loss: 168.480, reward: 13.951, critic_reward: 14.094, revenue_rate: 0.3554, distance: 5.4717, memory: -0.0049, power: 0.1666, lr: 0.000050, took: 75.424s
Epoch 1, Batch 30/3125, loss: 10.969, reward: 13.386, critic_reward: 12.681, revenue_rate: 0.3431, distance: 5.3121, memory: -0.0023, power: 0.1571, lr: 0.000050, took: 73.952s
Epoch 1, Batch 40/3125, loss: 24.555, reward: 13.573, critic_reward: 12.812, revenue_rate: 0.3455, distance: 5.2637, memory: 0.0007, power: 0.1601, lr: 0.000050, took: 75.254s
Epoch 1, Batch 50/3125, loss: 16.112, reward: 11.173, critic_reward: 12.820, revenue_rate: 0.2868, distance: 4.5240, memory: 0.0303, power: 0.1379, lr: 0.000050, took: 63.425s
Epoch 1, Batch 60/3125, loss: 7.639, reward: 10.877, critic_reward: 11.206, revenue_rate: 0.2806, distance: 4.5086, memory: 0.0446, power: 0.1365, lr: 0.000050, took: 58.944s
Epoch 1, Batch 70/3125, loss: 8.686, reward: 10.726, critic_reward: 11.517, revenue_rate: 0.2763, distance: 4.4231, memory: 0.0313, power: 0.1333, lr: 0.000050, took: 57.253s
Epoch 1, Batch 80/3125, loss: 8.114, reward: 10.549, critic_reward: 10.668, revenue_rate: 0.2725, distance: 4.3940, memory: 0.0433, power: 0.1337, lr: 0.000050, took: 59.784s
Epoch 1, Batch 90/3125, loss: 6.350, reward: 10.709, critic_reward: 11.501, revenue_rate: 0.2792, distance: 4.5661, memory: 0.0622, power: 0.1385, lr: 0.000050, took: 60.983s
Epoch 1, Batch 100/3125, loss: 4.300, reward: 10.399, critic_reward: 10.097, revenue_rate: 0.2694, distance: 4.4116, memory: 0.0666, power: 0.1344, lr: 0.000050, took: 59.742s
Epoch 1, Batch 110/3125, loss: 9.224, reward: 10.442, critic_reward: 9.576, revenue_rate: 0.2714, distance: 4.4432, memory: 0.0503, power: 0.1335, lr: 0.000050, took: 61.560s
Epoch 1, Batch 120/3125, loss: 4.464, reward: 10.390, critic_reward: 10.306, revenue_rate: 0.2696, distance: 4.3535, memory: 0.0522, power: 0.1346, lr: 0.000050, took: 59.979s
Epoch 1, Batch 130/3125, loss: 6.196, reward: 11.029, critic_reward: 11.209, revenue_rate: 0.2821, distance: 4.4431, memory: 0.0541, power: 0.1391, lr: 0.000050, took: 64.657s
Epoch 1, Batch 140/3125, loss: 5.669, reward: 11.592, critic_reward: 11.043, revenue_rate: 0.2990, distance: 4.7580, memory: 0.0575, power: 0.1470, lr: 0.000050, took: 65.305s
Epoch 1, Batch 150/3125, loss: 3.727, reward: 12.504, critic_reward: 12.657, revenue_rate: 0.3245, distance: 5.1977, memory: 0.0568, power: 0.1572, lr: 0.000050, took: 72.314s
Epoch 1, Batch 160/3125, loss: 12.372, reward: 12.277, critic_reward: 11.975, revenue_rate: 0.3170, distance: 5.1126, memory: 0.0458, power: 0.1544, lr: 0.000050, took: 72.262s
Epoch 1, Batch 170/3125, loss: 6.587, reward: 13.105, critic_reward: 13.348, revenue_rate: 0.3371, distance: 5.4591, memory: 0.0498, power: 0.1648, lr: 0.000050, took: 75.379s
Epoch 1, Batch 180/3125, loss: 9.758, reward: 13.369, critic_reward: 14.031, revenue_rate: 0.3444, distance: 5.5886, memory: 0.0434, power: 0.1690, lr: 0.000050, took: 75.890s
Epoch 1, Batch 190/3125, loss: 16.163, reward: 12.748, critic_reward: 13.389, revenue_rate: 0.3314, distance: 5.3065, memory: 0.0457, power: 0.1613, lr: 0.000050, took: 74.100s
Epoch 1, Batch 200/3125, loss: 7.811, reward: 12.962, critic_reward: 13.192, revenue_rate: 0.3333, distance: 5.2755, memory: 0.0442, power: 0.1621, lr: 0.000050, took: 73.024s
Epoch 1, Batch 210/3125, loss: 13.018, reward: 12.336, critic_reward: 13.990, revenue_rate: 0.3188, distance: 5.0843, memory: 0.0371, power: 0.1544, lr: 0.000050, took: 70.481s
Epoch 1, Batch 220/3125, loss: 8.506, reward: 12.758, critic_reward: 12.325, revenue_rate: 0.3274, distance: 5.1418, memory: 0.0287, power: 0.1564, lr: 0.000050, took: 71.829s
Epoch 1, Batch 230/3125, loss: 5.940, reward: 12.380, critic_reward: 12.318, revenue_rate: 0.3169, distance: 5.0152, memory: 0.0117, power: 0.1494, lr: 0.000050, took: 68.750s
Epoch 1, Batch 240/3125, loss: 6.085, reward: 11.342, critic_reward: 10.773, revenue_rate: 0.2924, distance: 4.6683, memory: 0.0331, power: 0.1388, lr: 0.000050, took: 61.375s
Epoch 1, Batch 250/3125, loss: 4.696, reward: 9.887, critic_reward: 9.982, revenue_rate: 0.2549, distance: 4.0020, memory: 0.0252, power: 0.1232, lr: 0.000050, took: 57.543s
Epoch 1, Batch 260/3125, loss: 5.544, reward: 10.677, critic_reward: 10.244, revenue_rate: 0.2755, distance: 4.4064, memory: 0.0279, power: 0.1345, lr: 0.000050, took: 62.290s
Epoch 1, Batch 270/3125, loss: 4.567, reward: 10.801, critic_reward: 10.488, revenue_rate: 0.2762, distance: 4.2627, memory: 0.0151, power: 0.1315, lr: 0.000050, took: 58.752s
Epoch 1, Batch 280/3125, loss: 3.725, reward: 10.911, critic_reward: 10.871, revenue_rate: 0.2774, distance: 4.3384, memory: 0.0092, power: 0.1313, lr: 0.000050, took: 59.401s
Epoch 1, Batch 290/3125, loss: 4.947, reward: 11.439, critic_reward: 11.271, revenue_rate: 0.2944, distance: 4.6128, memory: 0.0028, power: 0.1386, lr: 0.000050, took: 62.394s
Epoch 1, Batch 300/3125, loss: 5.937, reward: 12.653, critic_reward: 12.767, revenue_rate: 0.3261, distance: 5.0983, memory: 0.0342, power: 0.1566, lr: 0.000050, took: 69.737s
Epoch 1, Batch 310/3125, loss: 5.398, reward: 12.541, critic_reward: 12.348, revenue_rate: 0.3232, distance: 5.1166, memory: 0.0355, power: 0.1560, lr: 0.000050, took: 71.195s
Epoch 1, Batch 320/3125, loss: 9.725, reward: 12.455, critic_reward: 14.035, revenue_rate: 0.3197, distance: 4.9986, memory: 0.0408, power: 0.1545, lr: 0.000050, took: 69.817s
Epoch 1, Batch 330/3125, loss: 9.821, reward: 12.078, critic_reward: 10.631, revenue_rate: 0.3123, distance: 5.0371, memory: 0.0583, power: 0.1532, lr: 0.000050, took: 68.743s
Epoch 1, Batch 340/3125, loss: 7.402, reward: 11.252, critic_reward: 12.711, revenue_rate: 0.2933, distance: 4.8543, memory: 0.0760, power: 0.1448, lr: 0.000050, took: 66.627s
Epoch 1, Batch 350/3125, loss: 3.529, reward: 10.833, critic_reward: 10.550, revenue_rate: 0.2819, distance: 4.6381, memory: 0.0710, power: 0.1402, lr: 0.000050, took: 63.549s
Epoch 1, Batch 360/3125, loss: 3.573, reward: 10.147, critic_reward: 10.196, revenue_rate: 0.2632, distance: 4.2127, memory: 0.0728, power: 0.1291, lr: 0.000050, took: 58.888s
Epoch 1, Batch 370/3125, loss: 3.822, reward: 9.652, critic_reward: 9.633, revenue_rate: 0.2507, distance: 4.0938, memory: 0.0738, power: 0.1247, lr: 0.000050, took: 56.860s
Epoch 1, Batch 380/3125, loss: 3.233, reward: 9.430, critic_reward: 9.311, revenue_rate: 0.2441, distance: 4.0270, memory: 0.0643, power: 0.1202, lr: 0.000050, took: 53.901s
Epoch 1, Batch 390/3125, loss: 3.115, reward: 8.937, critic_reward: 8.593, revenue_rate: 0.2328, distance: 3.8330, memory: 0.0554, power: 0.1159, lr: 0.000050, took: 52.986s
Epoch 1, Batch 400/3125, loss: 4.115, reward: 9.304, critic_reward: 9.142, revenue_rate: 0.2412, distance: 3.9172, memory: 0.0474, power: 0.1185, lr: 0.000050, took: 53.605s
Epoch 1, Batch 410/3125, loss: 3.932, reward: 9.598, critic_reward: 9.706, revenue_rate: 0.2478, distance: 3.9784, memory: 0.0561, power: 0.1216, lr: 0.000050, took: 55.539s
Epoch 1, Batch 420/3125, loss: 2.801, reward: 9.346, critic_reward: 9.208, revenue_rate: 0.2409, distance: 3.8404, memory: 0.0539, power: 0.1197, lr: 0.000050, took: 52.994s
Epoch 1, Batch 430/3125, loss: 3.539, reward: 8.961, critic_reward: 9.098, revenue_rate: 0.2314, distance: 3.6864, memory: 0.0574, power: 0.1143, lr: 0.000050, took: 52.401s
Epoch 1, Batch 440/3125, loss: 4.520, reward: 9.130, critic_reward: 8.889, revenue_rate: 0.2366, distance: 3.8437, memory: 0.0406, power: 0.1155, lr: 0.000050, took: 52.183s
Epoch 1, Batch 450/3125, loss: 3.940, reward: 8.893, critic_reward: 8.803, revenue_rate: 0.2322, distance: 3.8776, memory: 0.0595, power: 0.1146, lr: 0.000050, took: 52.760s
Epoch 1, Batch 460/3125, loss: 3.861, reward: 9.408, critic_reward: 9.541, revenue_rate: 0.2441, distance: 4.0253, memory: 0.0614, power: 0.1217, lr: 0.000050, took: 53.597s
Epoch 1, Batch 470/3125, loss: 3.261, reward: 9.800, critic_reward: 9.826, revenue_rate: 0.2515, distance: 3.9966, memory: 0.0455, power: 0.1226, lr: 0.000050, took: 55.182s
Epoch 1, Batch 480/3125, loss: 3.157, reward: 9.565, critic_reward: 9.665, revenue_rate: 0.2479, distance: 3.9916, memory: 0.0546, power: 0.1223, lr: 0.000050, took: 53.718s
Epoch 1, Batch 490/3125, loss: 6.835, reward: 9.177, critic_reward: 7.999, revenue_rate: 0.2364, distance: 3.8111, memory: 0.0405, power: 0.1155, lr: 0.000050, took: 52.265s
Epoch 1, Batch 500/3125, loss: 3.966, reward: 9.036, critic_reward: 9.191, revenue_rate: 0.2350, distance: 3.8181, memory: 0.0551, power: 0.1164, lr: 0.000050, took: 52.598s
Epoch 1, Batch 510/3125, loss: 4.419, reward: 9.411, critic_reward: 9.666, revenue_rate: 0.2429, distance: 3.9800, memory: 0.0380, power: 0.1179, lr: 0.000050, took: 55.149s
Epoch 1, Batch 520/3125, loss: 4.229, reward: 9.224, critic_reward: 9.361, revenue_rate: 0.2412, distance: 4.0220, memory: 0.0448, power: 0.1195, lr: 0.000050, took: 53.172s
Epoch 1, Batch 530/3125, loss: 3.715, reward: 10.158, critic_reward: 10.285, revenue_rate: 0.2603, distance: 4.1577, memory: 0.0427, power: 0.1266, lr: 0.000050, took: 56.204s
Epoch 1, Batch 540/3125, loss: 3.405, reward: 9.173, critic_reward: 9.503, revenue_rate: 0.2393, distance: 3.9653, memory: 0.0365, power: 0.1187, lr: 0.000050, took: 55.635s
Epoch 1, Batch 550/3125, loss: 3.610, reward: 9.729, critic_reward: 9.445, revenue_rate: 0.2522, distance: 4.1209, memory: 0.0406, power: 0.1237, lr: 0.000050, took: 54.324s
Epoch 1, Batch 560/3125, loss: 5.305, reward: 9.181, critic_reward: 10.437, revenue_rate: 0.2407, distance: 3.9252, memory: 0.0527, power: 0.1201, lr: 0.000050, took: 52.782s
Epoch 1, Batch 570/3125, loss: 3.725, reward: 9.030, critic_reward: 8.337, revenue_rate: 0.2314, distance: 3.7042, memory: 0.0322, power: 0.1145, lr: 0.000050, took: 51.985s
Epoch 1, Batch 580/3125, loss: 5.425, reward: 8.959, critic_reward: 9.901, revenue_rate: 0.2314, distance: 3.7680, memory: 0.0659, power: 0.1158, lr: 0.000050, took: 54.261s
Epoch 1, Batch 590/3125, loss: 3.029, reward: 8.424, critic_reward: 8.544, revenue_rate: 0.2184, distance: 3.5856, memory: 0.0556, power: 0.1092, lr: 0.000050, took: 48.722s
Epoch 1, Batch 600/3125, loss: 3.757, reward: 8.389, critic_reward: 7.665, revenue_rate: 0.2170, distance: 3.5893, memory: 0.0606, power: 0.1092, lr: 0.000050, took: 48.559s
Epoch 1, Batch 610/3125, loss: 3.635, reward: 8.515, critic_reward: 8.978, revenue_rate: 0.2192, distance: 3.5227, memory: 0.0469, power: 0.1068, lr: 0.000050, took: 48.230s
Epoch 1, Batch 620/3125, loss: 2.975, reward: 8.413, critic_reward: 8.280, revenue_rate: 0.2173, distance: 3.5213, memory: 0.0410, power: 0.1076, lr: 0.000050, took: 48.747s
Epoch 1, Batch 630/3125, loss: 3.593, reward: 9.194, critic_reward: 9.064, revenue_rate: 0.2382, distance: 3.8387, memory: 0.0447, power: 0.1171, lr: 0.000050, took: 49.639s
Epoch 1, Batch 640/3125, loss: 2.885, reward: 8.371, critic_reward: 8.560, revenue_rate: 0.2175, distance: 3.5592, memory: 0.0551, power: 0.1068, lr: 0.000050, took: 46.627s
Epoch 1, Batch 650/3125, loss: 2.194, reward: 8.052, critic_reward: 8.069, revenue_rate: 0.2085, distance: 3.3747, memory: 0.0563, power: 0.1039, lr: 0.000050, took: 48.504s
Epoch 1, Batch 660/3125, loss: 1.957, reward: 7.761, critic_reward: 7.547, revenue_rate: 0.2013, distance: 3.3243, memory: 0.0677, power: 0.1021, lr: 0.000050, took: 45.559s
Epoch 1, Batch 670/3125, loss: 3.783, reward: 7.784, critic_reward: 7.329, revenue_rate: 0.2026, distance: 3.2808, memory: 0.0438, power: 0.1005, lr: 0.000050, took: 45.405s
Epoch 1, Batch 680/3125, loss: 2.416, reward: 7.555, critic_reward: 7.740, revenue_rate: 0.1970, distance: 3.2252, memory: 0.0563, power: 0.0989, lr: 0.000050, took: 44.586s
Epoch 1, Batch 690/3125, loss: 2.927, reward: 7.763, critic_reward: 7.682, revenue_rate: 0.2020, distance: 3.2914, memory: 0.0446, power: 0.1010, lr: 0.000050, took: 44.988s
Epoch 1, Batch 700/3125, loss: 2.776, reward: 8.081, critic_reward: 8.318, revenue_rate: 0.2104, distance: 3.4895, memory: 0.0459, power: 0.1053, lr: 0.000050, took: 47.766s
Epoch 1, Batch 710/3125, loss: 2.487, reward: 7.957, critic_reward: 8.184, revenue_rate: 0.2070, distance: 3.3781, memory: 0.0524, power: 0.1037, lr: 0.000050, took: 46.073s
Epoch 1, Batch 720/3125, loss: 3.550, reward: 8.506, critic_reward: 9.030, revenue_rate: 0.2211, distance: 3.5920, memory: 0.0379, power: 0.1071, lr: 0.000050, took: 48.509s
Epoch 1, Batch 730/3125, loss: 3.970, reward: 8.363, critic_reward: 9.098, revenue_rate: 0.2159, distance: 3.4939, memory: 0.0416, power: 0.1065, lr: 0.000050, took: 47.376s
Epoch 1, Batch 740/3125, loss: 4.713, reward: 8.542, critic_reward: 8.911, revenue_rate: 0.2230, distance: 3.6470, memory: 0.0523, power: 0.1096, lr: 0.000050, took: 50.380s
Epoch 1, Batch 750/3125, loss: 4.934, reward: 8.473, critic_reward: 8.805, revenue_rate: 0.2203, distance: 3.6113, memory: 0.0571, power: 0.1094, lr: 0.000050, took: 48.207s
Epoch 1, Batch 760/3125, loss: 3.823, reward: 8.000, critic_reward: 7.435, revenue_rate: 0.2085, distance: 3.4377, memory: 0.0645, power: 0.1061, lr: 0.000050, took: 47.392s
Epoch 1, Batch 770/3125, loss: 3.416, reward: 8.392, critic_reward: 8.482, revenue_rate: 0.2185, distance: 3.5507, memory: 0.0415, power: 0.1090, lr: 0.000050, took: 50.186s
Epoch 1, Batch 780/3125, loss: 3.302, reward: 8.371, critic_reward: 8.376, revenue_rate: 0.2169, distance: 3.5070, memory: 0.0503, power: 0.1079, lr: 0.000050, took: 47.785s
Epoch 1, Batch 790/3125, loss: 3.821, reward: 8.019, critic_reward: 7.559, revenue_rate: 0.2084, distance: 3.3907, memory: 0.0463, power: 0.1037, lr: 0.000050, took: 47.375s
Epoch 1, Batch 800/3125, loss: 3.433, reward: 8.331, critic_reward: 8.535, revenue_rate: 0.2155, distance: 3.4794, memory: 0.0517, power: 0.1075, lr: 0.000050, took: 47.969s
Epoch 1, Batch 810/3125, loss: 2.610, reward: 8.588, critic_reward: 8.645, revenue_rate: 0.2220, distance: 3.6356, memory: 0.0536, power: 0.1104, lr: 0.000050, took: 49.684s
Epoch 1, Batch 820/3125, loss: 2.762, reward: 8.225, critic_reward: 8.501, revenue_rate: 0.2141, distance: 3.5238, memory: 0.0451, power: 0.1075, lr: 0.000050, took: 48.573s
Epoch 1, Batch 830/3125, loss: 3.755, reward: 8.416, critic_reward: 7.796, revenue_rate: 0.2174, distance: 3.5446, memory: 0.0508, power: 0.1095, lr: 0.000050, took: 50.297s
Epoch 1, Batch 840/3125, loss: 2.559, reward: 8.294, critic_reward: 8.347, revenue_rate: 0.2154, distance: 3.5455, memory: 0.0474, power: 0.1067, lr: 0.000050, took: 47.778s
Epoch 1, Batch 850/3125, loss: 3.920, reward: 7.858, critic_reward: 8.013, revenue_rate: 0.2037, distance: 3.3206, memory: 0.0509, power: 0.1008, lr: 0.000050, took: 46.806s
Epoch 1, Batch 860/3125, loss: 2.338, reward: 8.302, critic_reward: 8.612, revenue_rate: 0.2168, distance: 3.5415, memory: 0.0537, power: 0.1074, lr: 0.000050, took: 48.057s
Epoch 1, Batch 870/3125, loss: 4.584, reward: 8.447, critic_reward: 8.295, revenue_rate: 0.2200, distance: 3.6489, memory: 0.0464, power: 0.1095, lr: 0.000050, took: 46.516s
Epoch 1, Batch 880/3125, loss: 3.604, reward: 8.225, critic_reward: 8.436, revenue_rate: 0.2120, distance: 3.4239, memory: 0.0540, power: 0.1043, lr: 0.000050, took: 47.989s
Epoch 1, Batch 890/3125, loss: 2.387, reward: 8.305, critic_reward: 8.561, revenue_rate: 0.2151, distance: 3.4848, memory: 0.0566, power: 0.1077, lr: 0.000050, took: 47.321s
Epoch 1, Batch 900/3125, loss: 2.788, reward: 8.429, critic_reward: 8.483, revenue_rate: 0.2184, distance: 3.5297, memory: 0.0500, power: 0.1089, lr: 0.000050, took: 46.655s
Epoch 1, Batch 910/3125, loss: 2.456, reward: 8.007, critic_reward: 8.415, revenue_rate: 0.2074, distance: 3.3305, memory: 0.0487, power: 0.1010, lr: 0.000050, took: 44.592s
Epoch 1, Batch 920/3125, loss: 2.321, reward: 7.460, critic_reward: 7.676, revenue_rate: 0.1938, distance: 3.2037, memory: 0.0516, power: 0.0956, lr: 0.000050, took: 44.765s
Epoch 1, Batch 930/3125, loss: 4.519, reward: 8.400, critic_reward: 7.881, revenue_rate: 0.2163, distance: 3.5174, memory: 0.0489, power: 0.1068, lr: 0.000050, took: 47.194s
Epoch 1, Batch 940/3125, loss: 2.648, reward: 7.975, critic_reward: 8.323, revenue_rate: 0.2062, distance: 3.3178, memory: 0.0530, power: 0.1012, lr: 0.000050, took: 45.963s
Epoch 1, Batch 950/3125, loss: 3.018, reward: 7.944, critic_reward: 7.504, revenue_rate: 0.2060, distance: 3.4016, memory: 0.0638, power: 0.1043, lr: 0.000050, took: 45.401s
Epoch 1, Batch 960/3125, loss: 2.759, reward: 8.115, critic_reward: 7.810, revenue_rate: 0.2111, distance: 3.4301, memory: 0.0562, power: 0.1044, lr: 0.000050, took: 47.563s
Epoch 1, Batch 970/3125, loss: 2.583, reward: 7.749, critic_reward: 8.280, revenue_rate: 0.1995, distance: 3.1984, memory: 0.0515, power: 0.0988, lr: 0.000050, took: 44.455s
Epoch 1, Batch 980/3125, loss: 2.756, reward: 7.514, critic_reward: 7.637, revenue_rate: 0.1949, distance: 3.1990, memory: 0.0533, power: 0.0974, lr: 0.000050, took: 44.408s
Epoch 1, Batch 990/3125, loss: 3.463, reward: 8.140, critic_reward: 8.933, revenue_rate: 0.2104, distance: 3.3955, memory: 0.0564, power: 0.1048, lr: 0.000050, took: 46.508s
Epoch 1, Batch 1000/3125, loss: 1.819, reward: 8.014, critic_reward: 7.855, revenue_rate: 0.2079, distance: 3.4102, memory: 0.0533, power: 0.1019, lr: 0.000050, took: 45.182s
Epoch 1, Batch 1010/3125, loss: 2.232, reward: 7.857, critic_reward: 7.933, revenue_rate: 0.2032, distance: 3.3269, memory: 0.0566, power: 0.1018, lr: 0.000050, took: 47.010s
Epoch 1, Batch 1020/3125, loss: 3.874, reward: 8.791, critic_reward: 9.521, revenue_rate: 0.2286, distance: 3.7735, memory: 0.0539, power: 0.1139, lr: 0.000050, took: 49.984s
Epoch 1, Batch 1030/3125, loss: 2.442, reward: 8.393, critic_reward: 8.418, revenue_rate: 0.2188, distance: 3.5948, memory: 0.0578, power: 0.1092, lr: 0.000050, took: 48.558s
Epoch 1, Batch 1040/3125, loss: 4.313, reward: 8.599, critic_reward: 7.832, revenue_rate: 0.2231, distance: 3.6216, memory: 0.0502, power: 0.1089, lr: 0.000050, took: 49.242s
Epoch 1, Batch 1050/3125, loss: 5.842, reward: 9.264, critic_reward: 8.539, revenue_rate: 0.2380, distance: 3.8153, memory: 0.0398, power: 0.1166, lr: 0.000050, took: 52.340s
Epoch 1, Batch 1060/3125, loss: 4.116, reward: 9.340, critic_reward: 9.539, revenue_rate: 0.2410, distance: 3.9222, memory: 0.0548, power: 0.1191, lr: 0.000050, took: 53.202s
Epoch 1, Batch 1070/3125, loss: 3.512, reward: 8.847, critic_reward: 8.730, revenue_rate: 0.2289, distance: 3.7170, memory: 0.0454, power: 0.1126, lr: 0.000050, took: 50.759s
Epoch 1, Batch 1080/3125, loss: 2.868, reward: 8.773, critic_reward: 8.428, revenue_rate: 0.2261, distance: 3.6534, memory: 0.0547, power: 0.1112, lr: 0.000050, took: 49.375s
Epoch 1, Batch 1090/3125, loss: 2.494, reward: 8.266, critic_reward: 8.518, revenue_rate: 0.2144, distance: 3.4760, memory: 0.0624, power: 0.1069, lr: 0.000050, took: 48.843s
Epoch 1, Batch 1100/3125, loss: 2.910, reward: 8.289, critic_reward: 8.042, revenue_rate: 0.2156, distance: 3.5376, memory: 0.0658, power: 0.1081, lr: 0.000050, took: 49.753s
Epoch 1, Batch 1110/3125, loss: 2.355, reward: 8.433, critic_reward: 8.925, revenue_rate: 0.2182, distance: 3.5292, memory: 0.0531, power: 0.1090, lr: 0.000050, took: 48.341s
Epoch 1, Batch 1120/3125, loss: 2.255, reward: 8.304, critic_reward: 8.241, revenue_rate: 0.2147, distance: 3.4487, memory: 0.0509, power: 0.1064, lr: 0.000050, took: 48.300s
Epoch 1, Batch 1130/3125, loss: 3.311, reward: 8.604, critic_reward: 8.399, revenue_rate: 0.2236, distance: 3.6218, memory: 0.0528, power: 0.1113, lr: 0.000050, took: 51.110s
Epoch 1, Batch 1140/3125, loss: 2.818, reward: 8.661, critic_reward: 8.828, revenue_rate: 0.2239, distance: 3.6659, memory: 0.0399, power: 0.1119, lr: 0.000050, took: 51.763s
Epoch 1, Batch 1150/3125, loss: 2.857, reward: 8.369, critic_reward: 8.041, revenue_rate: 0.2173, distance: 3.6555, memory: 0.0484, power: 0.1084, lr: 0.000050, took: 49.380s
Epoch 1, Batch 1160/3125, loss: 4.808, reward: 8.105, critic_reward: 9.460, revenue_rate: 0.2115, distance: 3.5046, memory: 0.0645, power: 0.1079, lr: 0.000050, took: 49.007s
Epoch 1, Batch 1170/3125, loss: 4.129, reward: 8.597, critic_reward: 9.466, revenue_rate: 0.2225, distance: 3.5767, memory: 0.0558, power: 0.1095, lr: 0.000050, took: 49.785s
Epoch 1, Batch 1180/3125, loss: 4.709, reward: 8.621, critic_reward: 7.784, revenue_rate: 0.2256, distance: 3.7700, memory: 0.0575, power: 0.1125, lr: 0.000050, took: 49.355s
Epoch 1, Batch 1190/3125, loss: 4.426, reward: 8.498, critic_reward: 8.474, revenue_rate: 0.2225, distance: 3.6541, memory: 0.0606, power: 0.1118, lr: 0.000050, took: 49.602s
Epoch 1, Batch 1200/3125, loss: 3.000, reward: 8.469, critic_reward: 8.434, revenue_rate: 0.2209, distance: 3.6314, memory: 0.0554, power: 0.1102, lr: 0.000050, took: 50.389s
Epoch 1, Batch 1210/3125, loss: 2.646, reward: 8.625, critic_reward: 8.274, revenue_rate: 0.2251, distance: 3.7266, memory: 0.0553, power: 0.1117, lr: 0.000050, took: 50.800s
Epoch 1, Batch 1220/3125, loss: 2.842, reward: 8.437, critic_reward: 8.044, revenue_rate: 0.2191, distance: 3.5763, memory: 0.0558, power: 0.1087, lr: 0.000050, took: 50.080s
Epoch 1, Batch 1230/3125, loss: 3.070, reward: 9.502, critic_reward: 9.140, revenue_rate: 0.2461, distance: 3.9892, memory: 0.0535, power: 0.1211, lr: 0.000050, took: 51.265s
Epoch 1, Batch 1240/3125, loss: 2.891, reward: 8.919, critic_reward: 9.036, revenue_rate: 0.2312, distance: 3.7364, memory: 0.0462, power: 0.1140, lr: 0.000050, took: 50.986s
Epoch 1, Batch 1250/3125, loss: 2.896, reward: 8.628, critic_reward: 8.061, revenue_rate: 0.2253, distance: 3.7383, memory: 0.0671, power: 0.1133, lr: 0.000050, took: 48.301s
Epoch 1, Batch 1260/3125, loss: 7.208, reward: 8.796, critic_reward: 10.579, revenue_rate: 0.2272, distance: 3.6981, memory: 0.0509, power: 0.1119, lr: 0.000050, took: 48.897s
Epoch 1, Batch 1270/3125, loss: 3.028, reward: 8.617, critic_reward: 8.099, revenue_rate: 0.2233, distance: 3.6509, memory: 0.0557, power: 0.1110, lr: 0.000050, took: 48.954s
Epoch 1, Batch 1280/3125, loss: 3.895, reward: 8.731, critic_reward: 8.789, revenue_rate: 0.2267, distance: 3.6788, memory: 0.0548, power: 0.1118, lr: 0.000050, took: 49.681s
Epoch 1, Batch 1290/3125, loss: 2.620, reward: 8.732, critic_reward: 8.901, revenue_rate: 0.2265, distance: 3.6845, memory: 0.0600, power: 0.1124, lr: 0.000050, took: 50.091s
Epoch 1, Batch 1300/3125, loss: 2.323, reward: 8.412, critic_reward: 8.655, revenue_rate: 0.2200, distance: 3.6061, memory: 0.0628, power: 0.1093, lr: 0.000050, took: 49.401s
Epoch 1, Batch 1310/3125, loss: 2.894, reward: 8.846, critic_reward: 8.903, revenue_rate: 0.2292, distance: 3.6982, memory: 0.0610, power: 0.1135, lr: 0.000050, took: 50.590s
Epoch 1, Batch 1320/3125, loss: 2.751, reward: 8.594, critic_reward: 8.679, revenue_rate: 0.2227, distance: 3.6245, memory: 0.0537, power: 0.1116, lr: 0.000050, took: 50.769s
Epoch 1, Batch 1330/3125, loss: 4.711, reward: 8.970, critic_reward: 9.029, revenue_rate: 0.2315, distance: 3.7625, memory: 0.0516, power: 0.1132, lr: 0.000050, took: 49.596s
Epoch 1, Batch 1340/3125, loss: 3.722, reward: 8.500, critic_reward: 7.908, revenue_rate: 0.2209, distance: 3.5673, memory: 0.0497, power: 0.1091, lr: 0.000050, took: 48.573s
Epoch 1, Batch 1350/3125, loss: 4.645, reward: 8.080, critic_reward: 8.947, revenue_rate: 0.2107, distance: 3.4969, memory: 0.0603, power: 0.1054, lr: 0.000050, took: 46.813s
Epoch 1, Batch 1360/3125, loss: 4.039, reward: 8.187, critic_reward: 8.513, revenue_rate: 0.2116, distance: 3.4658, memory: 0.0513, power: 0.1049, lr: 0.000050, took: 46.530s
Epoch 1, Batch 1370/3125, loss: 2.534, reward: 8.608, critic_reward: 8.739, revenue_rate: 0.2225, distance: 3.5946, memory: 0.0587, power: 0.1118, lr: 0.000050, took: 47.634s
Epoch 1, Batch 1380/3125, loss: 2.338, reward: 7.929, critic_reward: 7.838, revenue_rate: 0.2066, distance: 3.3767, memory: 0.0615, power: 0.1046, lr: 0.000050, took: 45.960s
Epoch 1, Batch 1390/3125, loss: 2.877, reward: 8.037, critic_reward: 8.423, revenue_rate: 0.2090, distance: 3.4075, memory: 0.0632, power: 0.1041, lr: 0.000050, took: 47.179s
Epoch 1, Batch 1400/3125, loss: 2.424, reward: 8.461, critic_reward: 8.405, revenue_rate: 0.2174, distance: 3.4431, memory: 0.0500, power: 0.1067, lr: 0.000050, took: 47.603s
Epoch 1, Batch 1410/3125, loss: 2.450, reward: 7.949, critic_reward: 7.989, revenue_rate: 0.2074, distance: 3.4299, memory: 0.0539, power: 0.1037, lr: 0.000050, took: 47.169s
Epoch 1, Batch 1420/3125, loss: 4.099, reward: 7.953, critic_reward: 8.770, revenue_rate: 0.2065, distance: 3.3838, memory: 0.0563, power: 0.1058, lr: 0.000050, took: 48.998s
Epoch 1, Batch 1430/3125, loss: 3.609, reward: 8.460, critic_reward: 8.577, revenue_rate: 0.2187, distance: 3.5154, memory: 0.0491, power: 0.1092, lr: 0.000050, took: 47.997s
Epoch 1, Batch 1440/3125, loss: 2.718, reward: 8.415, critic_reward: 8.324, revenue_rate: 0.2188, distance: 3.5043, memory: 0.0443, power: 0.1085, lr: 0.000050, took: 48.178s
Epoch 1, Batch 1450/3125, loss: 2.579, reward: 8.505, critic_reward: 8.238, revenue_rate: 0.2227, distance: 3.7508, memory: 0.0625, power: 0.1125, lr: 0.000050, took: 50.058s
Epoch 1, Batch 1460/3125, loss: 3.272, reward: 8.224, critic_reward: 9.007, revenue_rate: 0.2136, distance: 3.4654, memory: 0.0674, power: 0.1077, lr: 0.000050, took: 48.284s
Epoch 1, Batch 1470/3125, loss: 3.084, reward: 8.701, critic_reward: 8.072, revenue_rate: 0.2257, distance: 3.6726, memory: 0.0529, power: 0.1129, lr: 0.000050, took: 49.393s
Epoch 1, Batch 1480/3125, loss: 4.953, reward: 7.864, critic_reward: 9.280, revenue_rate: 0.2060, distance: 3.4377, memory: 0.0575, power: 0.1031, lr: 0.000050, took: 47.609s
Epoch 1, Batch 1490/3125, loss: 2.543, reward: 8.103, critic_reward: 7.644, revenue_rate: 0.2109, distance: 3.3970, memory: 0.0578, power: 0.1038, lr: 0.000050, took: 47.365s
Epoch 1, Batch 1500/3125, loss: 4.252, reward: 8.341, critic_reward: 9.542, revenue_rate: 0.2187, distance: 3.6489, memory: 0.0613, power: 0.1097, lr: 0.000050, took: 46.957s
Epoch 1, Batch 1510/3125, loss: 3.405, reward: 8.006, critic_reward: 7.048, revenue_rate: 0.2096, distance: 3.4778, memory: 0.0621, power: 0.1053, lr: 0.000050, took: 49.392s
Epoch 1, Batch 1520/3125, loss: 2.886, reward: 8.846, critic_reward: 8.775, revenue_rate: 0.2299, distance: 3.7406, memory: 0.0606, power: 0.1157, lr: 0.000050, took: 48.729s
Epoch 1, Batch 1530/3125, loss: 2.564, reward: 8.454, critic_reward: 7.947, revenue_rate: 0.2199, distance: 3.6479, memory: 0.0624, power: 0.1098, lr: 0.000050, took: 48.459s
Epoch 1, Batch 1540/3125, loss: 2.965, reward: 8.179, critic_reward: 8.377, revenue_rate: 0.2151, distance: 3.5839, memory: 0.0692, power: 0.1070, lr: 0.000050, took: 49.622s
Epoch 1, Batch 1550/3125, loss: 4.784, reward: 7.953, critic_reward: 9.312, revenue_rate: 0.2063, distance: 3.4095, memory: 0.0508, power: 0.1026, lr: 0.000050, took: 47.187s
Epoch 1, Batch 1560/3125, loss: 5.009, reward: 8.214, critic_reward: 7.057, revenue_rate: 0.2124, distance: 3.4794, memory: 0.0559, power: 0.1065, lr: 0.000050, took: 48.551s
Epoch 1, Batch 1570/3125, loss: 3.710, reward: 7.696, critic_reward: 7.125, revenue_rate: 0.2008, distance: 3.3265, memory: 0.0598, power: 0.1002, lr: 0.000050, took: 46.194s
Epoch 1, Batch 1580/3125, loss: 4.411, reward: 8.188, critic_reward: 8.644, revenue_rate: 0.2130, distance: 3.5154, memory: 0.0476, power: 0.1063, lr: 0.000050, took: 46.579s
Epoch 1, Batch 1590/3125, loss: 3.823, reward: 8.169, critic_reward: 7.839, revenue_rate: 0.2128, distance: 3.4600, memory: 0.0606, power: 0.1059, lr: 0.000050, took: 47.628s
Epoch 1, Batch 1600/3125, loss: 2.810, reward: 8.606, critic_reward: 8.650, revenue_rate: 0.2238, distance: 3.6441, memory: 0.0551, power: 0.1119, lr: 0.000050, took: 49.232s
Epoch 1, Batch 1610/3125, loss: 3.588, reward: 8.496, critic_reward: 7.784, revenue_rate: 0.2178, distance: 3.4132, memory: 0.0416, power: 0.1055, lr: 0.000050, took: 47.918s
Epoch 1, Batch 1620/3125, loss: 3.025, reward: 8.043, critic_reward: 8.489, revenue_rate: 0.2111, distance: 3.5444, memory: 0.0540, power: 0.1060, lr: 0.000050, took: 46.911s
Epoch 1, Batch 1630/3125, loss: 2.435, reward: 8.576, critic_reward: 8.778, revenue_rate: 0.2237, distance: 3.6849, memory: 0.0659, power: 0.1116, lr: 0.000050, took: 49.647s
Epoch 1, Batch 1640/3125, loss: 4.339, reward: 8.665, critic_reward: 8.754, revenue_rate: 0.2233, distance: 3.5464, memory: 0.0502, power: 0.1093, lr: 0.000050, took: 48.249s
Epoch 1, Batch 1650/3125, loss: 2.029, reward: 7.670, critic_reward: 7.548, revenue_rate: 0.1993, distance: 3.3073, memory: 0.0602, power: 0.1015, lr: 0.000050, took: 45.520s
Epoch 1, Batch 1660/3125, loss: 4.108, reward: 7.910, critic_reward: 7.284, revenue_rate: 0.2049, distance: 3.3812, memory: 0.0572, power: 0.1034, lr: 0.000050, took: 47.188s
Epoch 1, Batch 1670/3125, loss: 3.758, reward: 8.047, critic_reward: 7.586, revenue_rate: 0.2097, distance: 3.4079, memory: 0.0535, power: 0.1041, lr: 0.000050, took: 46.328s
Epoch 1, Batch 1680/3125, loss: 3.606, reward: 8.289, critic_reward: 9.138, revenue_rate: 0.2157, distance: 3.5209, memory: 0.0555, power: 0.1083, lr: 0.000050, took: 47.414s
Epoch 1, Batch 1690/3125, loss: 3.840, reward: 7.840, critic_reward: 7.608, revenue_rate: 0.2040, distance: 3.3524, memory: 0.0472, power: 0.1016, lr: 0.000050, took: 44.593s
Epoch 1, Batch 1700/3125, loss: 2.152, reward: 7.667, critic_reward: 7.777, revenue_rate: 0.1991, distance: 3.2732, memory: 0.0479, power: 0.0982, lr: 0.000050, took: 45.633s
Epoch 1, Batch 1710/3125, loss: 2.266, reward: 7.758, critic_reward: 7.986, revenue_rate: 0.2024, distance: 3.3321, memory: 0.0600, power: 0.1005, lr: 0.000050, took: 42.978s
Epoch 1, Batch 1720/3125, loss: 2.616, reward: 7.683, critic_reward: 8.072, revenue_rate: 0.1999, distance: 3.3187, memory: 0.0508, power: 0.0998, lr: 0.000050, took: 45.778s
Epoch 1, Batch 1730/3125, loss: 2.000, reward: 7.785, critic_reward: 7.637, revenue_rate: 0.2020, distance: 3.3101, memory: 0.0558, power: 0.1015, lr: 0.000050, took: 45.103s
Epoch 1, Batch 1740/3125, loss: 2.231, reward: 7.657, critic_reward: 8.010, revenue_rate: 0.1989, distance: 3.2393, memory: 0.0533, power: 0.1005, lr: 0.000050, took: 44.403s
Epoch 1, Batch 1750/3125, loss: 2.141, reward: 7.287, critic_reward: 6.956, revenue_rate: 0.1897, distance: 3.1749, memory: 0.0616, power: 0.0960, lr: 0.000050, took: 43.646s
Epoch 1, Batch 1760/3125, loss: 3.028, reward: 7.670, critic_reward: 8.191, revenue_rate: 0.1985, distance: 3.2327, memory: 0.0528, power: 0.0995, lr: 0.000050, took: 44.193s
Epoch 1, Batch 1770/3125, loss: 2.193, reward: 7.732, critic_reward: 7.957, revenue_rate: 0.2017, distance: 3.3132, memory: 0.0444, power: 0.1013, lr: 0.000050, took: 45.561s
Epoch 1, Batch 1780/3125, loss: 2.710, reward: 7.945, critic_reward: 7.253, revenue_rate: 0.2047, distance: 3.3515, memory: 0.0583, power: 0.1036, lr: 0.000050, took: 45.006s
Epoch 1, Batch 1790/3125, loss: 2.363, reward: 7.377, critic_reward: 7.609, revenue_rate: 0.1920, distance: 3.1169, memory: 0.0551, power: 0.0970, lr: 0.000050, took: 42.617s
Epoch 1, Batch 1800/3125, loss: 1.945, reward: 7.298, critic_reward: 7.307, revenue_rate: 0.1896, distance: 3.1303, memory: 0.0593, power: 0.0966, lr: 0.000050, took: 43.151s
Epoch 1, Batch 1810/3125, loss: 2.367, reward: 7.366, critic_reward: 7.188, revenue_rate: 0.1918, distance: 3.1792, memory: 0.0561, power: 0.0970, lr: 0.000050, took: 42.383s
Epoch 1, Batch 1820/3125, loss: 1.653, reward: 7.170, critic_reward: 7.132, revenue_rate: 0.1862, distance: 3.0361, memory: 0.0409, power: 0.0943, lr: 0.000050, took: 41.349s
Epoch 1, Batch 1830/3125, loss: 2.309, reward: 7.603, critic_reward: 8.059, revenue_rate: 0.1969, distance: 3.1967, memory: 0.0419, power: 0.0975, lr: 0.000050, took: 42.224s
Epoch 1, Batch 1840/3125, loss: 2.436, reward: 7.431, critic_reward: 6.946, revenue_rate: 0.1932, distance: 3.1667, memory: 0.0398, power: 0.0963, lr: 0.000050, took: 40.929s
Epoch 1, Batch 1850/3125, loss: 1.788, reward: 7.359, critic_reward: 7.088, revenue_rate: 0.1900, distance: 3.1216, memory: 0.0492, power: 0.0948, lr: 0.000050, took: 42.651s
Epoch 1, Batch 1860/3125, loss: 1.974, reward: 7.522, critic_reward: 8.057, revenue_rate: 0.1936, distance: 3.1125, memory: 0.0488, power: 0.0952, lr: 0.000050, took: 42.969s
Epoch 1, Batch 1870/3125, loss: 2.401, reward: 7.362, critic_reward: 7.079, revenue_rate: 0.1917, distance: 3.1688, memory: 0.0375, power: 0.0943, lr: 0.000050, took: 42.693s
Epoch 1, Batch 1880/3125, loss: 2.468, reward: 7.683, critic_reward: 7.667, revenue_rate: 0.1980, distance: 3.2299, memory: 0.0374, power: 0.0975, lr: 0.000050, took: 43.875s
Epoch 1, Batch 1890/3125, loss: 1.933, reward: 7.269, critic_reward: 7.431, revenue_rate: 0.1879, distance: 3.0049, memory: 0.0374, power: 0.0933, lr: 0.000050, took: 42.597s
Epoch 1, Batch 1900/3125, loss: 2.385, reward: 7.173, critic_reward: 6.788, revenue_rate: 0.1866, distance: 3.0722, memory: 0.0464, power: 0.0934, lr: 0.000050, took: 43.986s
Epoch 1, Batch 1910/3125, loss: 2.397, reward: 7.503, critic_reward: 7.577, revenue_rate: 0.1961, distance: 3.2854, memory: 0.0623, power: 0.0985, lr: 0.000050, took: 44.190s
Epoch 1, Batch 1920/3125, loss: 1.666, reward: 7.225, critic_reward: 7.031, revenue_rate: 0.1879, distance: 3.1132, memory: 0.0567, power: 0.0941, lr: 0.000050, took: 43.589s
Epoch 1, Batch 1930/3125, loss: 2.021, reward: 7.230, critic_reward: 7.690, revenue_rate: 0.1868, distance: 3.0299, memory: 0.0530, power: 0.0926, lr: 0.000050, took: 42.192s
Epoch 1, Batch 1940/3125, loss: 1.841, reward: 7.536, critic_reward: 7.583, revenue_rate: 0.1955, distance: 3.1590, memory: 0.0372, power: 0.0962, lr: 0.000050, took: 43.601s
Epoch 1, Batch 1950/3125, loss: 2.758, reward: 7.757, critic_reward: 7.372, revenue_rate: 0.2007, distance: 3.2514, memory: 0.0509, power: 0.1006, lr: 0.000050, took: 43.382s
Epoch 1, Batch 1960/3125, loss: 2.549, reward: 7.776, critic_reward: 7.796, revenue_rate: 0.2019, distance: 3.2799, memory: 0.0434, power: 0.0986, lr: 0.000050, took: 44.329s
Epoch 1, Batch 1970/3125, loss: 1.713, reward: 7.197, critic_reward: 7.150, revenue_rate: 0.1866, distance: 3.0449, memory: 0.0577, power: 0.0934, lr: 0.000050, took: 43.634s
Epoch 1, Batch 1980/3125, loss: 2.472, reward: 7.594, critic_reward: 7.355, revenue_rate: 0.1977, distance: 3.2325, memory: 0.0544, power: 0.0981, lr: 0.000050, took: 44.001s
Epoch 1, Batch 1990/3125, loss: 2.719, reward: 7.483, critic_reward: 7.258, revenue_rate: 0.1941, distance: 3.1735, memory: 0.0551, power: 0.0974, lr: 0.000050, took: 43.765s
Epoch 1, Batch 2000/3125, loss: 2.411, reward: 7.416, critic_reward: 7.789, revenue_rate: 0.1936, distance: 3.2448, memory: 0.0517, power: 0.0968, lr: 0.000050, took: 43.571s
Epoch 1, Batch 2010/3125, loss: 3.092, reward: 8.028, critic_reward: 7.828, revenue_rate: 0.2093, distance: 3.4882, memory: 0.0611, power: 0.1045, lr: 0.000050, took: 46.192s
Epoch 1, Batch 2020/3125, loss: 3.159, reward: 8.156, critic_reward: 8.712, revenue_rate: 0.2123, distance: 3.4920, memory: 0.0464, power: 0.1048, lr: 0.000050, took: 46.667s
Epoch 1, Batch 2030/3125, loss: 2.313, reward: 7.950, critic_reward: 8.349, revenue_rate: 0.2061, distance: 3.3910, memory: 0.0577, power: 0.1031, lr: 0.000050, took: 48.122s
Epoch 1, Batch 2040/3125, loss: 2.022, reward: 7.912, critic_reward: 7.882, revenue_rate: 0.2057, distance: 3.3798, memory: 0.0573, power: 0.1030, lr: 0.000050, took: 45.978s
Epoch 1, Batch 2050/3125, loss: 4.068, reward: 8.225, critic_reward: 8.377, revenue_rate: 0.2133, distance: 3.4491, memory: 0.0481, power: 0.1053, lr: 0.000050, took: 45.630s
Epoch 1, Batch 2060/3125, loss: 2.348, reward: 7.935, critic_reward: 7.898, revenue_rate: 0.2062, distance: 3.3836, memory: 0.0466, power: 0.1033, lr: 0.000050, took: 46.285s
Epoch 1, Batch 2070/3125, loss: 2.095, reward: 7.702, critic_reward: 7.829, revenue_rate: 0.2012, distance: 3.2879, memory: 0.0476, power: 0.0990, lr: 0.000050, took: 45.828s
Epoch 1, Batch 2080/3125, loss: 3.659, reward: 8.192, critic_reward: 8.260, revenue_rate: 0.2134, distance: 3.5097, memory: 0.0565, power: 0.1079, lr: 0.000050, took: 48.194s
Epoch 1, Batch 2090/3125, loss: 7.092, reward: 8.491, critic_reward: 9.076, revenue_rate: 0.2203, distance: 3.6258, memory: 0.0625, power: 0.1113, lr: 0.000050, took: 47.598s
Epoch 1, Batch 2100/3125, loss: 2.609, reward: 8.581, critic_reward: 8.217, revenue_rate: 0.2215, distance: 3.5350, memory: 0.0537, power: 0.1095, lr: 0.000050, took: 49.785s
Epoch 1, Batch 2110/3125, loss: 5.791, reward: 8.605, critic_reward: 8.613, revenue_rate: 0.2227, distance: 3.7018, memory: 0.0431, power: 0.1103, lr: 0.000050, took: 49.383s
Epoch 1, Batch 2120/3125, loss: 4.796, reward: 9.159, critic_reward: 9.291, revenue_rate: 0.2373, distance: 3.7922, memory: 0.0395, power: 0.1156, lr: 0.000050, took: 49.746s
Epoch 1, Batch 2130/3125, loss: 2.771, reward: 8.703, critic_reward: 8.262, revenue_rate: 0.2251, distance: 3.6353, memory: 0.0225, power: 0.1109, lr: 0.000050, took: 50.415s
Epoch 1, Batch 2140/3125, loss: 2.723, reward: 8.777, critic_reward: 9.406, revenue_rate: 0.2287, distance: 3.6496, memory: 0.0340, power: 0.1117, lr: 0.000050, took: 49.797s
Epoch 1, Batch 2150/3125, loss: 3.408, reward: 8.190, critic_reward: 8.162, revenue_rate: 0.2113, distance: 3.3715, memory: 0.0340, power: 0.1018, lr: 0.000050, took: 47.965s
Epoch 1, Batch 2160/3125, loss: 2.402, reward: 8.746, critic_reward: 8.764, revenue_rate: 0.2255, distance: 3.6287, memory: 0.0353, power: 0.1091, lr: 0.000050, took: 47.802s
Epoch 1, Batch 2170/3125, loss: 2.939, reward: 8.579, critic_reward: 8.249, revenue_rate: 0.2230, distance: 3.6409, memory: 0.0524, power: 0.1118, lr: 0.000050, took: 49.170s
Epoch 1, Batch 2180/3125, loss: 2.994, reward: 8.690, critic_reward: 8.763, revenue_rate: 0.2243, distance: 3.5685, memory: 0.0440, power: 0.1105, lr: 0.000050, took: 48.408s
Epoch 1, Batch 2190/3125, loss: 2.447, reward: 8.561, critic_reward: 8.414, revenue_rate: 0.2233, distance: 3.6726, memory: 0.0472, power: 0.1122, lr: 0.000050, took: 50.311s
Epoch 1, Batch 2200/3125, loss: 2.870, reward: 9.121, critic_reward: 9.285, revenue_rate: 0.2358, distance: 3.7840, memory: 0.0293, power: 0.1143, lr: 0.000050, took: 50.463s
Epoch 1, Batch 2210/3125, loss: 3.520, reward: 8.800, critic_reward: 8.902, revenue_rate: 0.2284, distance: 3.7262, memory: 0.0416, power: 0.1118, lr: 0.000050, took: 50.369s
Epoch 1, Batch 2220/3125, loss: 2.758, reward: 9.013, critic_reward: 8.862, revenue_rate: 0.2330, distance: 3.7704, memory: 0.0533, power: 0.1160, lr: 0.000050, took: 50.566s
Epoch 1, Batch 2230/3125, loss: 2.758, reward: 8.844, critic_reward: 9.204, revenue_rate: 0.2276, distance: 3.6614, memory: 0.0355, power: 0.1112, lr: 0.000050, took: 50.983s
Epoch 1, Batch 2240/3125, loss: 3.098, reward: 9.088, critic_reward: 8.768, revenue_rate: 0.2345, distance: 3.8132, memory: 0.0426, power: 0.1150, lr: 0.000050, took: 52.385s
Epoch 1, Batch 2250/3125, loss: 2.830, reward: 9.008, critic_reward: 8.672, revenue_rate: 0.2351, distance: 3.9138, memory: 0.0630, power: 0.1183, lr: 0.000050, took: 53.209s
Epoch 1, Batch 2260/3125, loss: 4.418, reward: 9.417, critic_reward: 9.312, revenue_rate: 0.2427, distance: 3.8904, memory: 0.0421, power: 0.1181, lr: 0.000050, took: 53.764s
Epoch 1, Batch 2270/3125, loss: 3.170, reward: 8.978, critic_reward: 9.370, revenue_rate: 0.2326, distance: 3.8020, memory: 0.0451, power: 0.1144, lr: 0.000050, took: 52.444s
Epoch 1, Batch 2280/3125, loss: 3.052, reward: 9.409, critic_reward: 9.345, revenue_rate: 0.2434, distance: 3.9849, memory: 0.0525, power: 0.1203, lr: 0.000050, took: 55.957s
Epoch 1, Batch 2290/3125, loss: 2.613, reward: 9.593, critic_reward: 9.592, revenue_rate: 0.2488, distance: 4.0363, memory: 0.0568, power: 0.1224, lr: 0.000050, took: 55.361s
Epoch 1, Batch 2300/3125, loss: 3.349, reward: 9.727, critic_reward: 9.842, revenue_rate: 0.2501, distance: 4.0318, memory: 0.0403, power: 0.1217, lr: 0.000050, took: 55.383s
Epoch 1, Batch 2310/3125, loss: 4.280, reward: 10.383, critic_reward: 10.023, revenue_rate: 0.2666, distance: 4.2223, memory: 0.0367, power: 0.1308, lr: 0.000050, took: 56.991s
Epoch 1, Batch 2320/3125, loss: 3.131, reward: 9.605, critic_reward: 9.755, revenue_rate: 0.2499, distance: 4.0061, memory: 0.0414, power: 0.1239, lr: 0.000050, took: 55.343s
Epoch 1, Batch 2330/3125, loss: 3.745, reward: 9.685, critic_reward: 9.430, revenue_rate: 0.2498, distance: 4.0445, memory: 0.0356, power: 0.1227, lr: 0.000050, took: 55.675s
Epoch 1, Batch 2340/3125, loss: 2.745, reward: 9.617, critic_reward: 9.208, revenue_rate: 0.2480, distance: 4.0200, memory: 0.0418, power: 0.1215, lr: 0.000050, took: 55.733s
Epoch 1, Batch 2350/3125, loss: 3.229, reward: 9.420, critic_reward: 9.645, revenue_rate: 0.2452, distance: 4.0281, memory: 0.0590, power: 0.1227, lr: 0.000050, took: 55.642s
Epoch 1, Batch 2360/3125, loss: 3.462, reward: 9.775, critic_reward: 9.755, revenue_rate: 0.2524, distance: 4.1196, memory: 0.0466, power: 0.1248, lr: 0.000050, took: 54.528s
Epoch 1, Batch 2370/3125, loss: 3.888, reward: 9.756, critic_reward: 10.082, revenue_rate: 0.2525, distance: 4.1261, memory: 0.0397, power: 0.1262, lr: 0.000050, took: 55.853s
Epoch 1, Batch 2380/3125, loss: 3.210, reward: 9.289, critic_reward: 8.828, revenue_rate: 0.2398, distance: 3.9367, memory: 0.0573, power: 0.1194, lr: 0.000050, took: 54.621s
Epoch 1, Batch 2390/3125, loss: 2.594, reward: 9.443, critic_reward: 9.263, revenue_rate: 0.2445, distance: 4.0122, memory: 0.0646, power: 0.1223, lr: 0.000050, took: 54.634s
Epoch 1, Batch 2400/3125, loss: 3.114, reward: 8.994, critic_reward: 8.896, revenue_rate: 0.2335, distance: 3.8059, memory: 0.0481, power: 0.1154, lr: 0.000050, took: 53.632s
Epoch 1, Batch 2410/3125, loss: 5.024, reward: 10.147, critic_reward: 9.751, revenue_rate: 0.2628, distance: 4.1969, memory: 0.0445, power: 0.1304, lr: 0.000050, took: 56.370s
Epoch 1, Batch 2420/3125, loss: 5.752, reward: 10.242, critic_reward: 9.438, revenue_rate: 0.2647, distance: 4.2239, memory: 0.0431, power: 0.1280, lr: 0.000050, took: 57.387s
Epoch 1, Batch 2430/3125, loss: 3.097, reward: 9.985, critic_reward: 10.062, revenue_rate: 0.2585, distance: 4.2424, memory: 0.0374, power: 0.1260, lr: 0.000050, took: 58.180s
Epoch 1, Batch 2440/3125, loss: 2.578, reward: 10.349, critic_reward: 9.936, revenue_rate: 0.2681, distance: 4.3544, memory: 0.0507, power: 0.1318, lr: 0.000050, took: 60.571s
Epoch 1, Batch 2450/3125, loss: 4.648, reward: 10.528, critic_reward: 11.146, revenue_rate: 0.2697, distance: 4.3460, memory: 0.0435, power: 0.1305, lr: 0.000050, took: 59.626s
Epoch 1, Batch 2460/3125, loss: 3.438, reward: 10.150, critic_reward: 10.420, revenue_rate: 0.2635, distance: 4.2630, memory: 0.0456, power: 0.1289, lr: 0.000050, took: 59.542s
Epoch 1, Batch 2470/3125, loss: 3.714, reward: 10.258, critic_reward: 9.716, revenue_rate: 0.2667, distance: 4.3771, memory: 0.0529, power: 0.1329, lr: 0.000050, took: 58.806s
Epoch 1, Batch 2480/3125, loss: 4.439, reward: 10.747, critic_reward: 11.218, revenue_rate: 0.2797, distance: 4.5774, memory: 0.0461, power: 0.1378, lr: 0.000050, took: 58.756s
Epoch 1, Batch 2490/3125, loss: 4.180, reward: 9.595, critic_reward: 9.947, revenue_rate: 0.2477, distance: 4.0085, memory: 0.0357, power: 0.1221, lr: 0.000050, took: 57.035s
Epoch 1, Batch 2500/3125, loss: 4.682, reward: 9.837, critic_reward: 9.407, revenue_rate: 0.2556, distance: 4.1294, memory: 0.0470, power: 0.1268, lr: 0.000050, took: 56.922s
Epoch 1, Batch 2510/3125, loss: 3.675, reward: 10.239, critic_reward: 11.061, revenue_rate: 0.2672, distance: 4.4344, memory: 0.0549, power: 0.1325, lr: 0.000050, took: 56.978s
Epoch 1, Batch 2520/3125, loss: 7.069, reward: 10.319, critic_reward: 8.512, revenue_rate: 0.2664, distance: 4.3430, memory: 0.0607, power: 0.1306, lr: 0.000050, took: 58.198s
Epoch 1, Batch 2530/3125, loss: 4.012, reward: 10.486, critic_reward: 11.405, revenue_rate: 0.2720, distance: 4.4096, memory: 0.0396, power: 0.1332, lr: 0.000050, took: 59.192s
Epoch 1, Batch 2540/3125, loss: 2.942, reward: 10.542, critic_reward: 10.295, revenue_rate: 0.2717, distance: 4.4371, memory: 0.0558, power: 0.1345, lr: 0.000050, took: 59.657s
Epoch 1, Batch 2550/3125, loss: 3.658, reward: 10.285, critic_reward: 9.833, revenue_rate: 0.2655, distance: 4.3183, memory: 0.0569, power: 0.1318, lr: 0.000050, took: 58.294s
Epoch 1, Batch 2560/3125, loss: 2.976, reward: 10.078, critic_reward: 9.892, revenue_rate: 0.2595, distance: 4.1857, memory: 0.0283, power: 0.1269, lr: 0.000050, took: 58.220s
Epoch 1, Batch 2570/3125, loss: 3.084, reward: 10.378, critic_reward: 10.446, revenue_rate: 0.2697, distance: 4.4895, memory: 0.0601, power: 0.1355, lr: 0.000050, took: 60.362s
Epoch 1, Batch 2580/3125, loss: 3.634, reward: 10.521, critic_reward: 10.236, revenue_rate: 0.2743, distance: 4.4805, memory: 0.0547, power: 0.1365, lr: 0.000050, took: 61.579s
Epoch 1, Batch 2590/3125, loss: 2.779, reward: 10.390, critic_reward: 10.331, revenue_rate: 0.2694, distance: 4.4381, memory: 0.0543, power: 0.1336, lr: 0.000050, took: 63.229s
Epoch 1, Batch 2600/3125, loss: 3.036, reward: 10.737, critic_reward: 10.790, revenue_rate: 0.2760, distance: 4.4244, memory: 0.0396, power: 0.1354, lr: 0.000050, took: 61.902s
Epoch 1, Batch 2610/3125, loss: 4.642, reward: 10.728, critic_reward: 10.654, revenue_rate: 0.2789, distance: 4.5592, memory: 0.0454, power: 0.1375, lr: 0.000050, took: 62.809s
Epoch 1, Batch 2620/3125, loss: 3.456, reward: 10.908, critic_reward: 11.010, revenue_rate: 0.2836, distance: 4.6594, memory: 0.0547, power: 0.1416, lr: 0.000050, took: 64.184s
Epoch 1, Batch 2630/3125, loss: 3.181, reward: 10.739, critic_reward: 10.788, revenue_rate: 0.2780, distance: 4.5166, memory: 0.0431, power: 0.1379, lr: 0.000050, took: 63.031s
Epoch 1, Batch 2640/3125, loss: 7.187, reward: 11.462, critic_reward: 10.806, revenue_rate: 0.2977, distance: 4.7973, memory: 0.0424, power: 0.1456, lr: 0.000050, took: 65.534s
Epoch 1, Batch 2650/3125, loss: 4.659, reward: 11.019, critic_reward: 11.823, revenue_rate: 0.2854, distance: 4.6236, memory: 0.0585, power: 0.1411, lr: 0.000050, took: 64.220s
Epoch 1, Batch 2660/3125, loss: 3.977, reward: 11.108, critic_reward: 10.825, revenue_rate: 0.2875, distance: 4.6548, memory: 0.0535, power: 0.1428, lr: 0.000050, took: 64.776s
Epoch 1, Batch 2670/3125, loss: 4.475, reward: 10.666, critic_reward: 11.448, revenue_rate: 0.2762, distance: 4.5078, memory: 0.0573, power: 0.1380, lr: 0.000050, took: 62.514s
Epoch 1, Batch 2680/3125, loss: 3.550, reward: 10.770, critic_reward: 10.600, revenue_rate: 0.2788, distance: 4.5234, memory: 0.0431, power: 0.1382, lr: 0.000050, took: 63.658s
Epoch 1, Batch 2690/3125, loss: 5.146, reward: 10.833, critic_reward: 9.948, revenue_rate: 0.2790, distance: 4.5800, memory: 0.0542, power: 0.1395, lr: 0.000050, took: 62.307s
Epoch 1, Batch 2700/3125, loss: 6.507, reward: 11.398, critic_reward: 12.626, revenue_rate: 0.2950, distance: 4.8425, memory: 0.0451, power: 0.1474, lr: 0.000050, took: 64.013s
Epoch 1, Batch 2710/3125, loss: 7.614, reward: 11.195, critic_reward: 9.780, revenue_rate: 0.2895, distance: 4.6559, memory: 0.0457, power: 0.1414, lr: 0.000050, took: 62.863s
Epoch 1, Batch 2720/3125, loss: 3.494, reward: 10.682, critic_reward: 11.076, revenue_rate: 0.2763, distance: 4.4568, memory: 0.0305, power: 0.1344, lr: 0.000050, took: 62.072s
Epoch 1, Batch 2730/3125, loss: 3.841, reward: 10.695, critic_reward: 10.124, revenue_rate: 0.2756, distance: 4.4561, memory: 0.0527, power: 0.1359, lr: 0.000050, took: 60.664s
Epoch 1, Batch 2740/3125, loss: 2.575, reward: 10.503, critic_reward: 10.808, revenue_rate: 0.2710, distance: 4.4200, memory: 0.0531, power: 0.1321, lr: 0.000050, took: 62.682s
Epoch 1, Batch 2750/3125, loss: 3.150, reward: 10.916, critic_reward: 10.843, revenue_rate: 0.2831, distance: 4.5526, memory: 0.0524, power: 0.1388, lr: 0.000050, took: 62.463s
Epoch 1, Batch 2760/3125, loss: 3.862, reward: 10.984, critic_reward: 11.188, revenue_rate: 0.2836, distance: 4.5899, memory: 0.0403, power: 0.1381, lr: 0.000050, took: 62.734s
Epoch 1, Batch 2770/3125, loss: 3.855, reward: 10.518, critic_reward: 10.323, revenue_rate: 0.2735, distance: 4.4310, memory: 0.0570, power: 0.1342, lr: 0.000050, took: 61.222s
Epoch 1, Batch 2780/3125, loss: 5.607, reward: 10.754, critic_reward: 9.973, revenue_rate: 0.2794, distance: 4.5078, memory: 0.0358, power: 0.1370, lr: 0.000050, took: 60.128s
Epoch 1, Batch 2790/3125, loss: 3.572, reward: 10.330, critic_reward: 10.575, revenue_rate: 0.2696, distance: 4.4535, memory: 0.0502, power: 0.1345, lr: 0.000050, took: 61.954s
Epoch 1, Batch 2800/3125, loss: 3.109, reward: 10.365, critic_reward: 10.938, revenue_rate: 0.2705, distance: 4.4651, memory: 0.0619, power: 0.1345, lr: 0.000050, took: 60.587s
Epoch 1, Batch 2810/3125, loss: 3.181, reward: 10.775, critic_reward: 10.438, revenue_rate: 0.2803, distance: 4.5954, memory: 0.0501, power: 0.1384, lr: 0.000050, took: 62.182s
Epoch 1, Batch 2820/3125, loss: 4.974, reward: 10.299, critic_reward: 9.538, revenue_rate: 0.2684, distance: 4.3759, memory: 0.0482, power: 0.1333, lr: 0.000050, took: 60.778s
Epoch 1, Batch 2830/3125, loss: 5.328, reward: 10.921, critic_reward: 11.800, revenue_rate: 0.2817, distance: 4.5486, memory: 0.0540, power: 0.1396, lr: 0.000050, took: 63.182s
Epoch 1, Batch 2840/3125, loss: 5.080, reward: 10.863, critic_reward: 10.008, revenue_rate: 0.2803, distance: 4.5089, memory: 0.0395, power: 0.1377, lr: 0.000050, took: 62.998s
Epoch 1, Batch 2850/3125, loss: 4.620, reward: 10.999, critic_reward: 11.947, revenue_rate: 0.2840, distance: 4.5820, memory: 0.0423, power: 0.1398, lr: 0.000050, took: 63.764s
Epoch 1, Batch 2860/3125, loss: 4.800, reward: 11.321, critic_reward: 10.706, revenue_rate: 0.2938, distance: 4.7790, memory: 0.0562, power: 0.1465, lr: 0.000050, took: 64.011s
Epoch 1, Batch 2870/3125, loss: 3.504, reward: 11.323, critic_reward: 11.828, revenue_rate: 0.2933, distance: 4.7828, memory: 0.0506, power: 0.1445, lr: 0.000050, took: 64.026s
Epoch 1, Batch 2880/3125, loss: 3.724, reward: 10.538, critic_reward: 9.942, revenue_rate: 0.2725, distance: 4.3958, memory: 0.0385, power: 0.1336, lr: 0.000050, took: 65.235s
Epoch 1, Batch 2890/3125, loss: 4.516, reward: 10.810, critic_reward: 11.810, revenue_rate: 0.2785, distance: 4.4482, memory: 0.0212, power: 0.1328, lr: 0.000050, took: 62.064s
Epoch 1, Batch 2900/3125, loss: 4.456, reward: 10.107, critic_reward: 9.893, revenue_rate: 0.2633, distance: 4.3311, memory: 0.0464, power: 0.1305, lr: 0.000050, took: 59.577s
Epoch 1, Batch 2910/3125, loss: 5.180, reward: 10.514, critic_reward: 10.615, revenue_rate: 0.2724, distance: 4.3897, memory: 0.0413, power: 0.1331, lr: 0.000050, took: 59.777s
Epoch 1, Batch 2920/3125, loss: 4.889, reward: 11.164, critic_reward: 11.724, revenue_rate: 0.2881, distance: 4.5969, memory: 0.0446, power: 0.1395, lr: 0.000050, took: 60.876s
Epoch 1, Batch 2930/3125, loss: 3.605, reward: 10.990, critic_reward: 10.605, revenue_rate: 0.2838, distance: 4.5435, memory: 0.0293, power: 0.1394, lr: 0.000050, took: 61.066s
Epoch 1, Batch 2940/3125, loss: 4.354, reward: 10.832, critic_reward: 11.521, revenue_rate: 0.2784, distance: 4.4446, memory: 0.0358, power: 0.1369, lr: 0.000050, took: 61.599s
Epoch 1, Batch 2950/3125, loss: 4.470, reward: 10.478, critic_reward: 9.784, revenue_rate: 0.2724, distance: 4.4866, memory: 0.0421, power: 0.1353, lr: 0.000050, took: 61.168s
Epoch 1, Batch 2960/3125, loss: 5.763, reward: 11.145, critic_reward: 11.156, revenue_rate: 0.2888, distance: 4.6474, memory: 0.0394, power: 0.1431, lr: 0.000050, took: 63.193s
Epoch 1, Batch 2970/3125, loss: 4.311, reward: 10.754, critic_reward: 10.508, revenue_rate: 0.2782, distance: 4.4306, memory: 0.0501, power: 0.1352, lr: 0.000050, took: 62.355s
Epoch 1, Batch 2980/3125, loss: 2.923, reward: 10.695, critic_reward: 10.570, revenue_rate: 0.2768, distance: 4.4203, memory: 0.0524, power: 0.1347, lr: 0.000050, took: 62.180s
Epoch 1, Batch 2990/3125, loss: 3.784, reward: 10.802, critic_reward: 11.102, revenue_rate: 0.2798, distance: 4.5749, memory: 0.0402, power: 0.1377, lr: 0.000050, took: 63.008s
Epoch 1, Batch 3000/3125, loss: 2.926, reward: 10.883, critic_reward: 10.512, revenue_rate: 0.2827, distance: 4.6138, memory: 0.0460, power: 0.1396, lr: 0.000050, took: 62.621s
Epoch 1, Batch 3010/3125, loss: 5.158, reward: 11.017, critic_reward: 11.635, revenue_rate: 0.2848, distance: 4.6093, memory: 0.0383, power: 0.1392, lr: 0.000050, took: 64.426s
Epoch 1, Batch 3020/3125, loss: 3.157, reward: 11.285, critic_reward: 10.921, revenue_rate: 0.2911, distance: 4.6758, memory: 0.0317, power: 0.1405, lr: 0.000050, took: 62.722s
Epoch 1, Batch 3030/3125, loss: 7.006, reward: 11.094, critic_reward: 12.161, revenue_rate: 0.2864, distance: 4.6370, memory: 0.0451, power: 0.1423, lr: 0.000050, took: 65.545s
Epoch 1, Batch 3040/3125, loss: 4.157, reward: 11.149, critic_reward: 11.461, revenue_rate: 0.2887, distance: 4.6618, memory: 0.0429, power: 0.1412, lr: 0.000050, took: 62.972s
Epoch 1, Batch 3050/3125, loss: 4.885, reward: 11.127, critic_reward: 11.304, revenue_rate: 0.2880, distance: 4.6097, memory: 0.0313, power: 0.1405, lr: 0.000050, took: 64.872s
Epoch 1, Batch 3060/3125, loss: 4.489, reward: 11.515, critic_reward: 11.308, revenue_rate: 0.2969, distance: 4.7590, memory: 0.0262, power: 0.1451, lr: 0.000050, took: 63.702s
Epoch 1, Batch 3070/3125, loss: 3.751, reward: 11.016, critic_reward: 11.120, revenue_rate: 0.2856, distance: 4.6400, memory: 0.0354, power: 0.1420, lr: 0.000050, took: 60.973s
Epoch 1, Batch 3080/3125, loss: 4.680, reward: 11.172, critic_reward: 10.782, revenue_rate: 0.2886, distance: 4.5758, memory: 0.0288, power: 0.1420, lr: 0.000050, took: 64.458s
Epoch 1, Batch 3090/3125, loss: 3.162, reward: 10.668, critic_reward: 10.454, revenue_rate: 0.2760, distance: 4.4892, memory: 0.0286, power: 0.1341, lr: 0.000050, took: 61.496s
Epoch 1, Batch 3100/3125, loss: 3.567, reward: 10.875, critic_reward: 11.083, revenue_rate: 0.2825, distance: 4.6091, memory: 0.0625, power: 0.1413, lr: 0.000050, took: 61.786s
Epoch 1, Batch 3110/3125, loss: 4.079, reward: 10.725, critic_reward: 10.219, revenue_rate: 0.2789, distance: 4.4451, memory: 0.0342, power: 0.1375, lr: 0.000050, took: 61.404s
Epoch 1, Batch 3120/3125, loss: 3.656, reward: 11.149, critic_reward: 11.277, revenue_rate: 0.2860, distance: 4.6149, memory: 0.0416, power: 0.1375, lr: 0.000050, took: 62.575s
开始验证...
Test Batch 0/313, reward: 10.368, revenue_rate: 0.2687, distance: 4.3399, memory: 0.0730, power: 0.1329
Test Batch 1/313, reward: 10.482, revenue_rate: 0.2720, distance: 4.3300, memory: 0.0008, power: 0.1304
Test Batch 2/313, reward: 9.953, revenue_rate: 0.2609, distance: 4.5981, memory: 0.0803, power: 0.1343
Test Batch 3/313, reward: 11.269, revenue_rate: 0.3006, distance: 5.2374, memory: 0.0974, power: 0.1582
Test Batch 4/313, reward: 10.729, revenue_rate: 0.2819, distance: 4.7056, memory: 0.0351, power: 0.1385
Test Batch 5/313, reward: 10.792, revenue_rate: 0.2826, distance: 4.7169, memory: 0.0168, power: 0.1380
Test Batch 6/313, reward: 10.008, revenue_rate: 0.2670, distance: 4.5626, memory: 0.0695, power: 0.1386
Test Batch 7/313, reward: 12.139, revenue_rate: 0.3179, distance: 5.2143, memory: 0.0736, power: 0.1650
Test Batch 8/313, reward: 11.275, revenue_rate: 0.2936, distance: 5.0063, memory: 0.0808, power: 0.1497
Test Batch 9/313, reward: 9.860, revenue_rate: 0.2584, distance: 4.3363, memory: 0.0832, power: 0.1361
Test Batch 10/313, reward: 12.196, revenue_rate: 0.3075, distance: 4.7386, memory: 0.0667, power: 0.1536
Test Batch 11/313, reward: 10.662, revenue_rate: 0.2745, distance: 4.3329, memory: 0.0819, power: 0.1368
Test Batch 12/313, reward: 11.583, revenue_rate: 0.2975, distance: 4.7506, memory: 0.0015, power: 0.1436
Test Batch 13/313, reward: 10.322, revenue_rate: 0.2741, distance: 4.7337, memory: 0.0627, power: 0.1409
Test Batch 14/313, reward: 10.474, revenue_rate: 0.2728, distance: 4.4419, memory: -0.0113, power: 0.1349
Test Batch 15/313, reward: 11.052, revenue_rate: 0.2856, distance: 4.5596, memory: 0.0275, power: 0.1394
Test Batch 16/313, reward: 10.879, revenue_rate: 0.2756, distance: 4.2893, memory: 0.0069, power: 0.1262
Test Batch 17/313, reward: 12.091, revenue_rate: 0.3175, distance: 4.8897, memory: 0.0554, power: 0.1545
Test Batch 18/313, reward: 12.423, revenue_rate: 0.3169, distance: 5.3146, memory: 0.0558, power: 0.1595
Test Batch 19/313, reward: 11.010, revenue_rate: 0.2850, distance: 4.5081, memory: 0.0404, power: 0.1441
Test Batch 20/313, reward: 12.584, revenue_rate: 0.3336, distance: 5.7396, memory: 0.0382, power: 0.1662
Test Batch 21/313, reward: 8.895, revenue_rate: 0.2383, distance: 4.1224, memory: 0.0675, power: 0.1194
Test Batch 22/313, reward: 12.212, revenue_rate: 0.3107, distance: 5.0664, memory: -0.0138, power: 0.1479
Test Batch 23/313, reward: 11.998, revenue_rate: 0.3061, distance: 4.8534, memory: 0.0270, power: 0.1483
Test Batch 24/313, reward: 10.875, revenue_rate: 0.2843, distance: 4.5720, memory: 0.0243, power: 0.1417
Test Batch 25/313, reward: 11.249, revenue_rate: 0.2857, distance: 4.5760, memory: 0.0107, power: 0.1396
Test Batch 26/313, reward: 9.796, revenue_rate: 0.2560, distance: 4.3946, memory: 0.0698, power: 0.1243
Test Batch 27/313, reward: 11.659, revenue_rate: 0.3062, distance: 4.9941, memory: 0.0459, power: 0.1480
Test Batch 28/313, reward: 11.291, revenue_rate: 0.2916, distance: 4.6960, memory: 0.0642, power: 0.1461
Test Batch 29/313, reward: 10.260, revenue_rate: 0.2682, distance: 4.3369, memory: 0.0508, power: 0.1232
Test Batch 30/313, reward: 12.507, revenue_rate: 0.3189, distance: 4.8730, memory: -0.0014, power: 0.1518
Test Batch 31/313, reward: 10.693, revenue_rate: 0.2743, distance: 4.3495, memory: 0.0442, power: 0.1325
Test Batch 32/313, reward: 10.742, revenue_rate: 0.2766, distance: 4.5444, memory: 0.0775, power: 0.1383
Test Batch 33/313, reward: 10.763, revenue_rate: 0.2747, distance: 4.3248, memory: 0.0642, power: 0.1341
Test Batch 34/313, reward: 12.080, revenue_rate: 0.3074, distance: 4.9467, memory: 0.0076, power: 0.1473
Test Batch 35/313, reward: 11.489, revenue_rate: 0.3000, distance: 4.7754, memory: 0.0601, power: 0.1426
Test Batch 36/313, reward: 10.882, revenue_rate: 0.2845, distance: 4.8170, memory: 0.0571, power: 0.1423
Test Batch 37/313, reward: 11.504, revenue_rate: 0.2898, distance: 4.2982, memory: 0.0333, power: 0.1417
Test Batch 38/313, reward: 10.636, revenue_rate: 0.2742, distance: 4.2871, memory: 0.0080, power: 0.1270
Test Batch 39/313, reward: 10.900, revenue_rate: 0.2806, distance: 4.4472, memory: 0.0021, power: 0.1391
Test Batch 40/313, reward: 12.042, revenue_rate: 0.3174, distance: 5.5794, memory: 0.0440, power: 0.1612
Test Batch 41/313, reward: 11.298, revenue_rate: 0.3019, distance: 5.1242, memory: 0.0920, power: 0.1449
Test Batch 42/313, reward: 9.915, revenue_rate: 0.2573, distance: 4.1129, memory: 0.0015, power: 0.1240
Test Batch 43/313, reward: 11.588, revenue_rate: 0.2945, distance: 4.7079, memory: -0.0285, power: 0.1387
Test Batch 44/313, reward: 11.929, revenue_rate: 0.3082, distance: 4.7881, memory: -0.0301, power: 0.1407
Test Batch 45/313, reward: 11.233, revenue_rate: 0.2913, distance: 4.5266, memory: 0.0177, power: 0.1400
Test Batch 46/313, reward: 10.840, revenue_rate: 0.2883, distance: 4.7730, memory: 0.0587, power: 0.1455
Test Batch 47/313, reward: 9.792, revenue_rate: 0.2495, distance: 3.8304, memory: 0.0631, power: 0.1282
Test Batch 48/313, reward: 10.786, revenue_rate: 0.2768, distance: 4.2469, memory: 0.0196, power: 0.1309
Test Batch 49/313, reward: 10.558, revenue_rate: 0.2775, distance: 4.6272, memory: 0.0195, power: 0.1378
Test Batch 50/313, reward: 9.969, revenue_rate: 0.2607, distance: 4.2838, memory: 0.1073, power: 0.1334
Test Batch 51/313, reward: 10.806, revenue_rate: 0.2738, distance: 4.3879, memory: 0.0607, power: 0.1328
Test Batch 52/313, reward: 9.788, revenue_rate: 0.2526, distance: 4.1105, memory: 0.0530, power: 0.1231
Test Batch 53/313, reward: 12.652, revenue_rate: 0.3287, distance: 5.3549, memory: 0.0883, power: 0.1657
Test Batch 54/313, reward: 11.637, revenue_rate: 0.2989, distance: 4.7625, memory: -0.0329, power: 0.1341
Test Batch 55/313, reward: 10.287, revenue_rate: 0.2729, distance: 4.6770, memory: 0.0830, power: 0.1412
Test Batch 56/313, reward: 10.715, revenue_rate: 0.2720, distance: 4.2696, memory: 0.0213, power: 0.1284
Test Batch 57/313, reward: 12.355, revenue_rate: 0.3240, distance: 5.3566, memory: 0.0511, power: 0.1626
Test Batch 58/313, reward: 9.952, revenue_rate: 0.2551, distance: 4.1288, memory: 0.0301, power: 0.1255
Test Batch 59/313, reward: 11.061, revenue_rate: 0.2897, distance: 4.8856, memory: 0.0655, power: 0.1459
Test Batch 60/313, reward: 10.856, revenue_rate: 0.2850, distance: 4.6466, memory: 0.0533, power: 0.1514
Test Batch 61/313, reward: 9.604, revenue_rate: 0.2504, distance: 4.0094, memory: 0.0551, power: 0.1266
Test Batch 62/313, reward: 10.970, revenue_rate: 0.2815, distance: 4.4640, memory: 0.0566, power: 0.1334
Test Batch 63/313, reward: 11.173, revenue_rate: 0.2862, distance: 4.4666, memory: 0.0205, power: 0.1350
Test Batch 64/313, reward: 12.829, revenue_rate: 0.3303, distance: 5.4566, memory: 0.0683, power: 0.1611
Test Batch 65/313, reward: 10.629, revenue_rate: 0.2703, distance: 3.8814, memory: 0.0297, power: 0.1244
Test Batch 66/313, reward: 10.560, revenue_rate: 0.2704, distance: 3.9760, memory: -0.0082, power: 0.1255
Test Batch 67/313, reward: 10.642, revenue_rate: 0.2689, distance: 4.3202, memory: 0.0357, power: 0.1280
Test Batch 68/313, reward: 10.020, revenue_rate: 0.2546, distance: 3.9502, memory: 0.0461, power: 0.1203
Test Batch 69/313, reward: 11.264, revenue_rate: 0.2906, distance: 4.6655, memory: 0.0195, power: 0.1370
Test Batch 70/313, reward: 10.713, revenue_rate: 0.2798, distance: 4.6946, memory: 0.0465, power: 0.1452
Test Batch 71/313, reward: 11.070, revenue_rate: 0.2835, distance: 4.6097, memory: 0.0977, power: 0.1394
Test Batch 72/313, reward: 12.270, revenue_rate: 0.3161, distance: 4.9697, memory: 0.0206, power: 0.1500
Test Batch 73/313, reward: 9.940, revenue_rate: 0.2609, distance: 4.6481, memory: 0.1095, power: 0.1376
Test Batch 74/313, reward: 11.219, revenue_rate: 0.2888, distance: 4.6433, memory: 0.0605, power: 0.1398
Test Batch 75/313, reward: 10.851, revenue_rate: 0.2753, distance: 4.6662, memory: 0.0185, power: 0.1324
Test Batch 76/313, reward: 11.580, revenue_rate: 0.2986, distance: 4.5885, memory: -0.0170, power: 0.1376
Test Batch 77/313, reward: 12.267, revenue_rate: 0.3138, distance: 4.8358, memory: 0.0303, power: 0.1564
Test Batch 78/313, reward: 13.335, revenue_rate: 0.3470, distance: 5.7726, memory: 0.0642, power: 0.1675
Test Batch 79/313, reward: 12.079, revenue_rate: 0.3147, distance: 5.1954, memory: 0.0261, power: 0.1512
Test Batch 80/313, reward: 10.412, revenue_rate: 0.2666, distance: 4.2742, memory: 0.0267, power: 0.1305
Test Batch 81/313, reward: 10.417, revenue_rate: 0.2675, distance: 4.2415, memory: 0.0267, power: 0.1291
Test Batch 82/313, reward: 10.519, revenue_rate: 0.2751, distance: 4.4864, memory: 0.0339, power: 0.1356
Test Batch 83/313, reward: 11.403, revenue_rate: 0.2971, distance: 4.9033, memory: 0.0725, power: 0.1441
Test Batch 84/313, reward: 11.358, revenue_rate: 0.2878, distance: 4.4805, memory: 0.0234, power: 0.1424
Test Batch 85/313, reward: 9.100, revenue_rate: 0.2379, distance: 3.9797, memory: 0.0687, power: 0.1271
Test Batch 86/313, reward: 10.882, revenue_rate: 0.2807, distance: 4.5867, memory: 0.0144, power: 0.1404
Test Batch 87/313, reward: 9.985, revenue_rate: 0.2534, distance: 4.0200, memory: 0.0421, power: 0.1244
Test Batch 88/313, reward: 10.122, revenue_rate: 0.2656, distance: 4.2572, memory: 0.0834, power: 0.1337
Test Batch 89/313, reward: 10.859, revenue_rate: 0.2806, distance: 4.3327, memory: 0.0455, power: 0.1344
Test Batch 90/313, reward: 10.440, revenue_rate: 0.2721, distance: 4.3504, memory: 0.0559, power: 0.1448
Test Batch 91/313, reward: 10.416, revenue_rate: 0.2703, distance: 4.4696, memory: 0.0536, power: 0.1394
Test Batch 92/313, reward: 12.167, revenue_rate: 0.3102, distance: 4.7391, memory: 0.0153, power: 0.1427
Test Batch 93/313, reward: 11.740, revenue_rate: 0.3065, distance: 4.9931, memory: 0.0746, power: 0.1528
Test Batch 94/313, reward: 10.223, revenue_rate: 0.2648, distance: 4.1979, memory: 0.0052, power: 0.1264
Test Batch 95/313, reward: 10.033, revenue_rate: 0.2632, distance: 4.4646, memory: 0.0366, power: 0.1322
Test Batch 96/313, reward: 10.834, revenue_rate: 0.2775, distance: 4.2252, memory: 0.0123, power: 0.1316
Test Batch 97/313, reward: 11.811, revenue_rate: 0.3032, distance: 4.8432, memory: 0.0606, power: 0.1549
Test Batch 98/313, reward: 10.700, revenue_rate: 0.2750, distance: 4.2532, memory: 0.0211, power: 0.1378
Test Batch 99/313, reward: 11.104, revenue_rate: 0.2840, distance: 4.5002, memory: 0.0382, power: 0.1410
Test Batch 100/313, reward: 10.584, revenue_rate: 0.2686, distance: 4.4090, memory: 0.0501, power: 0.1361
Test Batch 101/313, reward: 11.567, revenue_rate: 0.2908, distance: 4.4781, memory: 0.0407, power: 0.1469
Test Batch 102/313, reward: 12.046, revenue_rate: 0.3147, distance: 5.1057, memory: 0.0526, power: 0.1497
Test Batch 103/313, reward: 12.164, revenue_rate: 0.3097, distance: 4.7096, memory: 0.0128, power: 0.1425
Test Batch 104/313, reward: 12.403, revenue_rate: 0.3216, distance: 5.2031, memory: 0.0742, power: 0.1613
Test Batch 105/313, reward: 9.586, revenue_rate: 0.2469, distance: 3.8751, memory: 0.0163, power: 0.1235
Test Batch 106/313, reward: 10.405, revenue_rate: 0.2657, distance: 4.1224, memory: 0.0298, power: 0.1309
Test Batch 107/313, reward: 9.039, revenue_rate: 0.2392, distance: 4.1941, memory: 0.0441, power: 0.1157
Test Batch 108/313, reward: 10.035, revenue_rate: 0.2585, distance: 4.2217, memory: 0.0358, power: 0.1229
Test Batch 109/313, reward: 11.147, revenue_rate: 0.2893, distance: 4.5942, memory: 0.0567, power: 0.1429
Test Batch 110/313, reward: 13.172, revenue_rate: 0.3396, distance: 5.4369, memory: 0.0428, power: 0.1583
Test Batch 111/313, reward: 10.783, revenue_rate: 0.2787, distance: 4.2918, memory: 0.0277, power: 0.1340
Test Batch 112/313, reward: 11.145, revenue_rate: 0.2856, distance: 4.7898, memory: 0.0378, power: 0.1385
Test Batch 113/313, reward: 9.748, revenue_rate: 0.2501, distance: 4.1924, memory: 0.0616, power: 0.1258
Test Batch 114/313, reward: 10.455, revenue_rate: 0.2729, distance: 4.3917, memory: 0.0080, power: 0.1275
Test Batch 115/313, reward: 10.337, revenue_rate: 0.2705, distance: 4.5344, memory: 0.0539, power: 0.1380
Test Batch 116/313, reward: 12.109, revenue_rate: 0.3212, distance: 5.2895, memory: 0.0752, power: 0.1682
Test Batch 117/313, reward: 11.438, revenue_rate: 0.2996, distance: 4.7967, memory: 0.0546, power: 0.1469
Test Batch 118/313, reward: 9.946, revenue_rate: 0.2597, distance: 4.3566, memory: 0.0867, power: 0.1333
Test Batch 119/313, reward: 10.923, revenue_rate: 0.2802, distance: 4.3916, memory: 0.0444, power: 0.1437
Test Batch 120/313, reward: 11.411, revenue_rate: 0.2993, distance: 4.9475, memory: 0.0748, power: 0.1499
Test Batch 121/313, reward: 10.855, revenue_rate: 0.2829, distance: 4.5595, memory: 0.0449, power: 0.1354
Test Batch 122/313, reward: 10.548, revenue_rate: 0.2737, distance: 4.3829, memory: 0.0717, power: 0.1367
Test Batch 123/313, reward: 11.059, revenue_rate: 0.2798, distance: 4.3686, memory: 0.0368, power: 0.1338
Test Batch 124/313, reward: 10.600, revenue_rate: 0.2700, distance: 4.4480, memory: 0.0515, power: 0.1323
Test Batch 125/313, reward: 10.362, revenue_rate: 0.2694, distance: 4.4287, memory: 0.0394, power: 0.1330
Test Batch 126/313, reward: 9.787, revenue_rate: 0.2517, distance: 4.0342, memory: 0.0177, power: 0.1187
Test Batch 127/313, reward: 9.873, revenue_rate: 0.2609, distance: 4.3505, memory: 0.0815, power: 0.1342
Test Batch 128/313, reward: 11.370, revenue_rate: 0.2884, distance: 4.4518, memory: 0.0255, power: 0.1380
Test Batch 129/313, reward: 10.767, revenue_rate: 0.2768, distance: 4.1986, memory: -0.0001, power: 0.1292
Test Batch 130/313, reward: 9.764, revenue_rate: 0.2558, distance: 4.3186, memory: 0.0347, power: 0.1254
Test Batch 131/313, reward: 10.823, revenue_rate: 0.2800, distance: 4.4820, memory: 0.0072, power: 0.1330
Test Batch 132/313, reward: 11.793, revenue_rate: 0.3078, distance: 5.0077, memory: 0.0610, power: 0.1575
Test Batch 133/313, reward: 10.347, revenue_rate: 0.2683, distance: 4.4484, memory: 0.0591, power: 0.1298
Test Batch 134/313, reward: 10.833, revenue_rate: 0.2841, distance: 4.6821, memory: 0.0736, power: 0.1403
Test Batch 135/313, reward: 10.906, revenue_rate: 0.2832, distance: 4.8935, memory: 0.1185, power: 0.1442
Test Batch 136/313, reward: 11.113, revenue_rate: 0.2926, distance: 4.8563, memory: 0.0845, power: 0.1451
Test Batch 137/313, reward: 11.140, revenue_rate: 0.2868, distance: 4.3861, memory: -0.0032, power: 0.1307
Test Batch 138/313, reward: 11.152, revenue_rate: 0.2901, distance: 4.6185, memory: 0.0088, power: 0.1377
Test Batch 139/313, reward: 10.603, revenue_rate: 0.2797, distance: 4.7989, memory: 0.0438, power: 0.1383
Test Batch 140/313, reward: 11.230, revenue_rate: 0.2833, distance: 4.0904, memory: -0.0020, power: 0.1330
Test Batch 141/313, reward: 10.273, revenue_rate: 0.2668, distance: 4.2601, memory: 0.0309, power: 0.1318
Test Batch 142/313, reward: 10.552, revenue_rate: 0.2696, distance: 4.1874, memory: 0.0117, power: 0.1291
Test Batch 143/313, reward: 9.828, revenue_rate: 0.2597, distance: 4.2114, memory: 0.0510, power: 0.1318
Test Batch 144/313, reward: 10.825, revenue_rate: 0.2777, distance: 4.3076, memory: 0.0264, power: 0.1339
Test Batch 145/313, reward: 11.114, revenue_rate: 0.2899, distance: 4.9568, memory: 0.0575, power: 0.1442
Test Batch 146/313, reward: 10.265, revenue_rate: 0.2634, distance: 4.4308, memory: 0.0744, power: 0.1332
Test Batch 147/313, reward: 12.204, revenue_rate: 0.3137, distance: 5.0866, memory: 0.0632, power: 0.1543
Test Batch 148/313, reward: 10.739, revenue_rate: 0.2748, distance: 4.4734, memory: 0.0516, power: 0.1354
Test Batch 149/313, reward: 10.979, revenue_rate: 0.2843, distance: 4.8194, memory: 0.0798, power: 0.1425
Test Batch 150/313, reward: 9.933, revenue_rate: 0.2638, distance: 4.6469, memory: 0.0680, power: 0.1385
Test Batch 151/313, reward: 11.716, revenue_rate: 0.3099, distance: 5.1067, memory: 0.0621, power: 0.1489
Test Batch 152/313, reward: 9.765, revenue_rate: 0.2488, distance: 3.8698, memory: 0.0131, power: 0.1131
Test Batch 153/313, reward: 9.731, revenue_rate: 0.2533, distance: 4.0904, memory: 0.0694, power: 0.1344
Test Batch 154/313, reward: 11.231, revenue_rate: 0.2935, distance: 4.6256, memory: -0.0127, power: 0.1383
Test Batch 155/313, reward: 9.702, revenue_rate: 0.2510, distance: 3.7383, memory: 0.0395, power: 0.1249
Test Batch 156/313, reward: 9.854, revenue_rate: 0.2545, distance: 3.9955, memory: 0.0138, power: 0.1259
Test Batch 157/313, reward: 10.550, revenue_rate: 0.2700, distance: 4.4115, memory: 0.0369, power: 0.1320
Test Batch 158/313, reward: 10.796, revenue_rate: 0.2774, distance: 4.6117, memory: 0.0913, power: 0.1369
Test Batch 159/313, reward: 11.532, revenue_rate: 0.3009, distance: 4.7656, memory: 0.0574, power: 0.1509
Test Batch 160/313, reward: 10.769, revenue_rate: 0.2771, distance: 4.3834, memory: 0.0517, power: 0.1387
Test Batch 161/313, reward: 10.877, revenue_rate: 0.2843, distance: 4.6126, memory: 0.0708, power: 0.1375
Test Batch 162/313, reward: 10.857, revenue_rate: 0.2806, distance: 4.6398, memory: 0.0673, power: 0.1412
Test Batch 163/313, reward: 11.612, revenue_rate: 0.3091, distance: 4.9686, memory: 0.0656, power: 0.1478
Test Batch 164/313, reward: 12.336, revenue_rate: 0.3139, distance: 4.9329, memory: -0.0095, power: 0.1485
Test Batch 165/313, reward: 10.401, revenue_rate: 0.2752, distance: 4.4300, memory: 0.0406, power: 0.1343
Test Batch 166/313, reward: 11.708, revenue_rate: 0.2991, distance: 4.8913, memory: 0.0227, power: 0.1458
Test Batch 167/313, reward: 11.389, revenue_rate: 0.2934, distance: 4.7624, memory: 0.0810, power: 0.1474
Test Batch 168/313, reward: 11.177, revenue_rate: 0.2915, distance: 4.8379, memory: 0.0281, power: 0.1427
Test Batch 169/313, reward: 10.586, revenue_rate: 0.2770, distance: 4.4592, memory: 0.0402, power: 0.1341
Test Batch 170/313, reward: 11.132, revenue_rate: 0.2937, distance: 4.7982, memory: 0.0348, power: 0.1468
Test Batch 171/313, reward: 14.098, revenue_rate: 0.3553, distance: 5.5284, memory: -0.0335, power: 0.1659
Test Batch 172/313, reward: 11.787, revenue_rate: 0.3025, distance: 4.7041, memory: 0.0331, power: 0.1441
Test Batch 173/313, reward: 10.372, revenue_rate: 0.2674, distance: 4.0841, memory: 0.0358, power: 0.1239
Test Batch 174/313, reward: 9.913, revenue_rate: 0.2599, distance: 4.1831, memory: 0.0848, power: 0.1255
Test Batch 175/313, reward: 11.388, revenue_rate: 0.2896, distance: 4.3610, memory: 0.0078, power: 0.1375
Test Batch 176/313, reward: 10.428, revenue_rate: 0.2674, distance: 4.0767, memory: 0.0190, power: 0.1285
Test Batch 177/313, reward: 9.409, revenue_rate: 0.2436, distance: 3.9378, memory: 0.0359, power: 0.1208
Test Batch 178/313, reward: 10.720, revenue_rate: 0.2757, distance: 4.4102, memory: -0.0011, power: 0.1327
Test Batch 179/313, reward: 10.902, revenue_rate: 0.2769, distance: 4.5066, memory: 0.0308, power: 0.1341
Test Batch 180/313, reward: 11.056, revenue_rate: 0.2870, distance: 4.6405, memory: 0.0098, power: 0.1378
Test Batch 181/313, reward: 10.083, revenue_rate: 0.2573, distance: 4.0749, memory: 0.0234, power: 0.1237
Test Batch 182/313, reward: 12.170, revenue_rate: 0.3053, distance: 4.7334, memory: 0.0190, power: 0.1396
Test Batch 183/313, reward: 10.362, revenue_rate: 0.2698, distance: 4.4628, memory: 0.0824, power: 0.1300
Test Batch 184/313, reward: 13.083, revenue_rate: 0.3360, distance: 5.4147, memory: 0.0593, power: 0.1671
Test Batch 185/313, reward: 12.246, revenue_rate: 0.3144, distance: 4.9214, memory: 0.0153, power: 0.1529
Test Batch 186/313, reward: 9.838, revenue_rate: 0.2591, distance: 4.5422, memory: 0.1019, power: 0.1368
Test Batch 187/313, reward: 10.306, revenue_rate: 0.2697, distance: 4.5171, memory: 0.0662, power: 0.1363
Test Batch 188/313, reward: 10.015, revenue_rate: 0.2594, distance: 4.1805, memory: 0.0727, power: 0.1296
Test Batch 189/313, reward: 10.684, revenue_rate: 0.2775, distance: 4.4772, memory: 0.0515, power: 0.1345
Test Batch 190/313, reward: 11.768, revenue_rate: 0.3044, distance: 4.9342, memory: 0.0669, power: 0.1509
Test Batch 191/313, reward: 10.278, revenue_rate: 0.2691, distance: 4.3637, memory: 0.0762, power: 0.1364
Test Batch 192/313, reward: 11.333, revenue_rate: 0.2947, distance: 4.5672, memory: 0.0600, power: 0.1447
Test Batch 193/313, reward: 12.101, revenue_rate: 0.3137, distance: 5.1250, memory: 0.0294, power: 0.1494
Test Batch 194/313, reward: 11.751, revenue_rate: 0.3052, distance: 4.9287, memory: 0.0332, power: 0.1484
Test Batch 195/313, reward: 13.914, revenue_rate: 0.3527, distance: 5.4521, memory: 0.0708, power: 0.1745
Test Batch 196/313, reward: 11.837, revenue_rate: 0.3130, distance: 5.1799, memory: 0.0414, power: 0.1554
Test Batch 197/313, reward: 10.822, revenue_rate: 0.2712, distance: 4.2003, memory: 0.0000, power: 0.1313
Test Batch 198/313, reward: 10.378, revenue_rate: 0.2698, distance: 4.2687, memory: 0.0428, power: 0.1252
Test Batch 199/313, reward: 11.303, revenue_rate: 0.2939, distance: 4.7767, memory: 0.0813, power: 0.1423
Test Batch 200/313, reward: 10.360, revenue_rate: 0.2700, distance: 4.4194, memory: 0.0480, power: 0.1384
Test Batch 201/313, reward: 10.594, revenue_rate: 0.2701, distance: 4.5347, memory: 0.0489, power: 0.1357
Test Batch 202/313, reward: 8.845, revenue_rate: 0.2336, distance: 4.0073, memory: 0.0849, power: 0.1241
Test Batch 203/313, reward: 10.972, revenue_rate: 0.2856, distance: 4.8891, memory: 0.0596, power: 0.1377
Test Batch 204/313, reward: 11.846, revenue_rate: 0.3099, distance: 4.8324, memory: 0.0419, power: 0.1524
Test Batch 205/313, reward: 11.729, revenue_rate: 0.3093, distance: 5.1430, memory: 0.0377, power: 0.1496
Test Batch 206/313, reward: 11.079, revenue_rate: 0.2883, distance: 4.7910, memory: 0.0270, power: 0.1421
Test Batch 207/313, reward: 10.392, revenue_rate: 0.2727, distance: 4.7058, memory: 0.0754, power: 0.1403
Test Batch 208/313, reward: 10.590, revenue_rate: 0.2745, distance: 4.4750, memory: 0.0614, power: 0.1334
Test Batch 209/313, reward: 9.998, revenue_rate: 0.2598, distance: 4.3777, memory: 0.0333, power: 0.1314
Test Batch 210/313, reward: 11.758, revenue_rate: 0.3016, distance: 4.7737, memory: 0.0117, power: 0.1418
Test Batch 211/313, reward: 12.583, revenue_rate: 0.3206, distance: 4.9722, memory: 0.0160, power: 0.1527
Test Batch 212/313, reward: 10.045, revenue_rate: 0.2643, distance: 4.4422, memory: 0.0442, power: 0.1297
Test Batch 213/313, reward: 12.157, revenue_rate: 0.3120, distance: 4.8254, memory: 0.0701, power: 0.1535
Test Batch 214/313, reward: 11.407, revenue_rate: 0.2940, distance: 4.5210, memory: 0.0072, power: 0.1323
Test Batch 215/313, reward: 10.905, revenue_rate: 0.2867, distance: 4.6345, memory: 0.0306, power: 0.1435
Test Batch 216/313, reward: 9.825, revenue_rate: 0.2555, distance: 4.2712, memory: 0.0701, power: 0.1334
Test Batch 217/313, reward: 10.610, revenue_rate: 0.2788, distance: 4.6252, memory: 0.0860, power: 0.1384
Test Batch 218/313, reward: 10.686, revenue_rate: 0.2739, distance: 4.3566, memory: 0.0497, power: 0.1336
Test Batch 219/313, reward: 11.006, revenue_rate: 0.2926, distance: 5.0172, memory: 0.1100, power: 0.1616
Test Batch 220/313, reward: 10.464, revenue_rate: 0.2725, distance: 4.2052, memory: 0.0175, power: 0.1306
Test Batch 221/313, reward: 11.164, revenue_rate: 0.2869, distance: 4.8647, memory: 0.0988, power: 0.1455
Test Batch 222/313, reward: 10.258, revenue_rate: 0.2618, distance: 4.0165, memory: 0.0442, power: 0.1295
Test Batch 223/313, reward: 11.766, revenue_rate: 0.3036, distance: 5.0076, memory: 0.0230, power: 0.1498
Test Batch 224/313, reward: 9.464, revenue_rate: 0.2431, distance: 4.0452, memory: 0.0056, power: 0.1202
Test Batch 225/313, reward: 10.838, revenue_rate: 0.2760, distance: 4.1895, memory: 0.0278, power: 0.1375
Test Batch 226/313, reward: 10.406, revenue_rate: 0.2694, distance: 4.4744, memory: 0.0621, power: 0.1330
Test Batch 227/313, reward: 11.831, revenue_rate: 0.2990, distance: 4.7168, memory: 0.0142, power: 0.1438
Test Batch 228/313, reward: 12.051, revenue_rate: 0.3072, distance: 4.7508, memory: 0.0114, power: 0.1429
Test Batch 229/313, reward: 12.005, revenue_rate: 0.3160, distance: 4.9145, memory: 0.0221, power: 0.1541
Test Batch 230/313, reward: 10.208, revenue_rate: 0.2689, distance: 4.3496, memory: 0.0630, power: 0.1346
Test Batch 231/313, reward: 9.930, revenue_rate: 0.2600, distance: 4.4754, memory: 0.0736, power: 0.1356
Test Batch 232/313, reward: 10.076, revenue_rate: 0.2634, distance: 4.3966, memory: 0.0433, power: 0.1268
Test Batch 233/313, reward: 10.959, revenue_rate: 0.2884, distance: 4.7583, memory: 0.0663, power: 0.1448
Test Batch 234/313, reward: 9.907, revenue_rate: 0.2550, distance: 3.8708, memory: 0.0275, power: 0.1186
Test Batch 235/313, reward: 10.567, revenue_rate: 0.2797, distance: 4.6110, memory: 0.0531, power: 0.1340
Test Batch 236/313, reward: 12.625, revenue_rate: 0.3276, distance: 5.1182, memory: 0.0173, power: 0.1582
Test Batch 237/313, reward: 11.436, revenue_rate: 0.2942, distance: 4.4988, memory: 0.0372, power: 0.1426
Test Batch 238/313, reward: 11.556, revenue_rate: 0.3008, distance: 4.9593, memory: 0.0284, power: 0.1437
Test Batch 239/313, reward: 12.003, revenue_rate: 0.3060, distance: 4.7457, memory: 0.0005, power: 0.1420
Test Batch 240/313, reward: 9.209, revenue_rate: 0.2344, distance: 3.7038, memory: 0.0416, power: 0.1172
Test Batch 241/313, reward: 10.319, revenue_rate: 0.2673, distance: 4.3121, memory: 0.0376, power: 0.1304
Test Batch 242/313, reward: 11.270, revenue_rate: 0.2843, distance: 4.2998, memory: -0.0156, power: 0.1321
Test Batch 243/313, reward: 10.596, revenue_rate: 0.2740, distance: 4.5412, memory: 0.0697, power: 0.1407
Test Batch 244/313, reward: 10.097, revenue_rate: 0.2601, distance: 4.1812, memory: 0.0498, power: 0.1254
Test Batch 245/313, reward: 11.343, revenue_rate: 0.2936, distance: 4.8991, memory: 0.0926, power: 0.1471
Test Batch 246/313, reward: 10.174, revenue_rate: 0.2645, distance: 4.2130, memory: 0.0330, power: 0.1258
Test Batch 247/313, reward: 10.585, revenue_rate: 0.2716, distance: 4.4271, memory: 0.0132, power: 0.1325
Test Batch 248/313, reward: 10.788, revenue_rate: 0.2813, distance: 4.6431, memory: 0.0652, power: 0.1405
Test Batch 249/313, reward: 12.058, revenue_rate: 0.3105, distance: 5.0635, memory: -0.0032, power: 0.1542
Test Batch 250/313, reward: 10.789, revenue_rate: 0.2838, distance: 4.7137, memory: 0.0456, power: 0.1439
Test Batch 251/313, reward: 9.683, revenue_rate: 0.2553, distance: 4.3106, memory: 0.0791, power: 0.1264
Test Batch 252/313, reward: 10.198, revenue_rate: 0.2668, distance: 4.4749, memory: 0.0487, power: 0.1358
Test Batch 253/313, reward: 11.358, revenue_rate: 0.2993, distance: 4.7192, memory: -0.0033, power: 0.1439
Test Batch 254/313, reward: 10.835, revenue_rate: 0.2787, distance: 4.2938, memory: -0.0167, power: 0.1336
Test Batch 255/313, reward: 11.442, revenue_rate: 0.2956, distance: 4.9327, memory: 0.0518, power: 0.1433
Test Batch 256/313, reward: 10.111, revenue_rate: 0.2644, distance: 4.6322, memory: 0.0766, power: 0.1312
Test Batch 257/313, reward: 8.835, revenue_rate: 0.2287, distance: 3.8695, memory: 0.0312, power: 0.1099
Test Batch 258/313, reward: 11.621, revenue_rate: 0.2985, distance: 4.9048, memory: 0.0273, power: 0.1443
Test Batch 259/313, reward: 10.220, revenue_rate: 0.2633, distance: 4.4430, memory: 0.0280, power: 0.1258
Test Batch 260/313, reward: 11.197, revenue_rate: 0.2836, distance: 4.5390, memory: 0.0451, power: 0.1409
Test Batch 261/313, reward: 9.887, revenue_rate: 0.2575, distance: 4.4050, memory: 0.0553, power: 0.1284
Test Batch 262/313, reward: 12.357, revenue_rate: 0.3169, distance: 4.8718, memory: 0.0383, power: 0.1567
Test Batch 263/313, reward: 13.256, revenue_rate: 0.3410, distance: 5.5532, memory: 0.0498, power: 0.1663
Test Batch 264/313, reward: 11.050, revenue_rate: 0.2846, distance: 4.2865, memory: -0.0013, power: 0.1343
Test Batch 265/313, reward: 9.816, revenue_rate: 0.2572, distance: 4.2614, memory: 0.0383, power: 0.1248
Test Batch 266/313, reward: 9.957, revenue_rate: 0.2601, distance: 4.1617, memory: 0.0099, power: 0.1264
Test Batch 267/313, reward: 11.292, revenue_rate: 0.2971, distance: 4.6794, memory: 0.0231, power: 0.1449
Test Batch 268/313, reward: 11.157, revenue_rate: 0.2873, distance: 4.7223, memory: 0.0417, power: 0.1407
Test Batch 269/313, reward: 11.977, revenue_rate: 0.3074, distance: 4.9100, memory: 0.0184, power: 0.1494
Test Batch 270/313, reward: 10.817, revenue_rate: 0.2829, distance: 4.6985, memory: 0.0358, power: 0.1434
Test Batch 271/313, reward: 10.272, revenue_rate: 0.2658, distance: 4.2980, memory: 0.0248, power: 0.1331
Test Batch 272/313, reward: 10.710, revenue_rate: 0.2762, distance: 4.2727, memory: -0.0027, power: 0.1288
Test Batch 273/313, reward: 10.767, revenue_rate: 0.2793, distance: 4.7011, memory: 0.0728, power: 0.1422
Test Batch 274/313, reward: 13.113, revenue_rate: 0.3416, distance: 5.4568, memory: 0.0108, power: 0.1628
Test Batch 275/313, reward: 9.820, revenue_rate: 0.2504, distance: 3.9032, memory: 0.0421, power: 0.1208
Test Batch 276/313, reward: 10.839, revenue_rate: 0.2826, distance: 4.6743, memory: 0.0887, power: 0.1410
Test Batch 277/313, reward: 9.910, revenue_rate: 0.2562, distance: 4.1866, memory: 0.0122, power: 0.1242
Test Batch 278/313, reward: 10.959, revenue_rate: 0.2800, distance: 4.7402, memory: 0.0576, power: 0.1475
Test Batch 279/313, reward: 10.493, revenue_rate: 0.2740, distance: 4.8022, memory: 0.0602, power: 0.1386
Test Batch 280/313, reward: 11.130, revenue_rate: 0.2911, distance: 4.8105, memory: 0.0104, power: 0.1380
Test Batch 281/313, reward: 11.599, revenue_rate: 0.2989, distance: 4.8100, memory: 0.0841, power: 0.1516
Test Batch 282/313, reward: 9.822, revenue_rate: 0.2541, distance: 4.1866, memory: 0.0635, power: 0.1305
Test Batch 283/313, reward: 11.227, revenue_rate: 0.2980, distance: 4.6893, memory: 0.0261, power: 0.1425
Test Batch 284/313, reward: 11.264, revenue_rate: 0.2934, distance: 4.6058, memory: 0.0771, power: 0.1520
Test Batch 285/313, reward: 11.582, revenue_rate: 0.2998, distance: 4.8766, memory: 0.0742, power: 0.1539
Test Batch 286/313, reward: 11.255, revenue_rate: 0.3030, distance: 5.2869, memory: 0.0944, power: 0.1527
Test Batch 287/313, reward: 10.060, revenue_rate: 0.2559, distance: 3.8964, memory: 0.0250, power: 0.1217
Test Batch 288/313, reward: 8.969, revenue_rate: 0.2322, distance: 3.7216, memory: 0.0337, power: 0.1127
Test Batch 289/313, reward: 10.400, revenue_rate: 0.2726, distance: 4.5266, memory: 0.0586, power: 0.1335
Test Batch 290/313, reward: 11.688, revenue_rate: 0.3034, distance: 5.1208, memory: 0.0604, power: 0.1500
Test Batch 291/313, reward: 10.123, revenue_rate: 0.2659, distance: 4.3735, memory: 0.0877, power: 0.1375
Test Batch 292/313, reward: 10.295, revenue_rate: 0.2656, distance: 4.3491, memory: 0.0450, power: 0.1228
Test Batch 293/313, reward: 11.071, revenue_rate: 0.2891, distance: 4.6541, memory: 0.0249, power: 0.1344
Test Batch 294/313, reward: 10.586, revenue_rate: 0.2774, distance: 4.5028, memory: 0.0489, power: 0.1388
Test Batch 295/313, reward: 11.490, revenue_rate: 0.2963, distance: 4.7704, memory: 0.0598, power: 0.1462
Test Batch 296/313, reward: 10.483, revenue_rate: 0.2687, distance: 4.5148, memory: 0.0699, power: 0.1377
Test Batch 297/313, reward: 11.716, revenue_rate: 0.2992, distance: 4.8132, memory: 0.0715, power: 0.1522
Test Batch 298/313, reward: 12.759, revenue_rate: 0.3274, distance: 5.1992, memory: -0.0314, power: 0.1515
Test Batch 299/313, reward: 12.838, revenue_rate: 0.3266, distance: 5.2429, memory: 0.0373, power: 0.1568
Test Batch 300/313, reward: 10.520, revenue_rate: 0.2736, distance: 4.3285, memory: 0.0596, power: 0.1344
Test Batch 301/313, reward: 10.791, revenue_rate: 0.2749, distance: 4.3342, memory: 0.0009, power: 0.1321
Test Batch 302/313, reward: 10.627, revenue_rate: 0.2821, distance: 4.7405, memory: 0.0812, power: 0.1437
Test Batch 303/313, reward: 10.239, revenue_rate: 0.2651, distance: 4.3421, memory: 0.0516, power: 0.1342
Test Batch 304/313, reward: 8.483, revenue_rate: 0.2216, distance: 3.5389, memory: 0.0155, power: 0.1099
Test Batch 305/313, reward: 8.894, revenue_rate: 0.2321, distance: 3.9204, memory: 0.0866, power: 0.1107
Test Batch 306/313, reward: 9.266, revenue_rate: 0.2425, distance: 3.8812, memory: 0.0339, power: 0.1156
Test Batch 307/313, reward: 10.750, revenue_rate: 0.2759, distance: 4.4281, memory: 0.0176, power: 0.1352
Test Batch 308/313, reward: 12.310, revenue_rate: 0.3202, distance: 5.0952, memory: 0.0501, power: 0.1543
Test Batch 309/313, reward: 10.443, revenue_rate: 0.2676, distance: 4.3072, memory: 0.0677, power: 0.1327
Test Batch 310/313, reward: 11.453, revenue_rate: 0.2979, distance: 4.7460, memory: 0.0083, power: 0.1467
Test Batch 311/313, reward: 11.454, revenue_rate: 0.2922, distance: 4.5259, memory: -0.0106, power: 0.1386
Test Batch 312/313, reward: 9.055, revenue_rate: 0.2367, distance: 3.9845, memory: 0.0183, power: 0.1112
Test Summary - Avg reward: 10.920, revenue_rate: 0.2827, distance: 4.5716, memory: 0.0426, power: 0.1387
验证完成 - Epoch 1, reward: 10.920, revenue_rate: 0.2827, distance: 4.5716, memory: 0.0426, power: 0.1387
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_20_21_51_52 (验证集奖励: 10.9201)

开始训练 Epoch 2/3
Epoch 2, Batch 10/3125, loss: 5.538, reward: 10.865, critic_reward: 9.551, revenue_rate: 0.2807, distance: 4.5125, memory: 0.0377, power: 0.1359, lr: 0.000050, took: 63.362s
Epoch 2, Batch 20/3125, loss: 5.687, reward: 11.471, critic_reward: 11.789, revenue_rate: 0.2977, distance: 4.7930, memory: 0.0412, power: 0.1448, lr: 0.000050, took: 64.380s
Epoch 2, Batch 30/3125, loss: 3.454, reward: 10.827, critic_reward: 10.286, revenue_rate: 0.2810, distance: 4.4856, memory: 0.0380, power: 0.1375, lr: 0.000050, took: 64.277s
Epoch 2, Batch 40/3125, loss: 5.114, reward: 11.634, critic_reward: 12.638, revenue_rate: 0.3003, distance: 4.8873, memory: 0.0351, power: 0.1468, lr: 0.000050, took: 64.078s
Epoch 2, Batch 50/3125, loss: 4.721, reward: 11.408, critic_reward: 11.058, revenue_rate: 0.2965, distance: 4.8250, memory: 0.0514, power: 0.1456, lr: 0.000050, took: 63.481s
Epoch 2, Batch 60/3125, loss: 4.177, reward: 10.738, critic_reward: 9.974, revenue_rate: 0.2782, distance: 4.5113, memory: 0.0375, power: 0.1359, lr: 0.000050, took: 62.608s
Epoch 2, Batch 70/3125, loss: 4.240, reward: 11.157, critic_reward: 11.927, revenue_rate: 0.2881, distance: 4.6502, memory: 0.0424, power: 0.1401, lr: 0.000050, took: 63.956s
Epoch 2, Batch 80/3125, loss: 4.634, reward: 10.910, critic_reward: 11.011, revenue_rate: 0.2798, distance: 4.4337, memory: 0.0249, power: 0.1370, lr: 0.000050, took: 61.808s
Epoch 2, Batch 90/3125, loss: 5.955, reward: 11.053, critic_reward: 10.744, revenue_rate: 0.2842, distance: 4.6093, memory: 0.0497, power: 0.1393, lr: 0.000050, took: 63.859s
Epoch 2, Batch 100/3125, loss: 3.537, reward: 10.533, critic_reward: 10.447, revenue_rate: 0.2738, distance: 4.5248, memory: 0.0493, power: 0.1344, lr: 0.000050, took: 69.086s
Epoch 2, Batch 110/3125, loss: 2.753, reward: 10.605, critic_reward: 10.516, revenue_rate: 0.2744, distance: 4.4240, memory: 0.0321, power: 0.1321, lr: 0.000050, took: 62.201s
Epoch 2, Batch 120/3125, loss: 3.681, reward: 11.109, critic_reward: 10.568, revenue_rate: 0.2855, distance: 4.6200, memory: 0.0528, power: 0.1418, lr: 0.000050, took: 64.229s
Epoch 2, Batch 130/3125, loss: 3.836, reward: 10.896, critic_reward: 10.987, revenue_rate: 0.2820, distance: 4.5828, memory: 0.0485, power: 0.1378, lr: 0.000050, took: 64.779s
Epoch 2, Batch 140/3125, loss: 3.076, reward: 11.022, critic_reward: 11.101, revenue_rate: 0.2842, distance: 4.5483, memory: 0.0395, power: 0.1382, lr: 0.000050, took: 62.728s
Epoch 2, Batch 150/3125, loss: 3.168, reward: 10.758, critic_reward: 10.918, revenue_rate: 0.2786, distance: 4.5249, memory: 0.0273, power: 0.1342, lr: 0.000050, took: 62.565s
Epoch 2, Batch 160/3125, loss: 4.117, reward: 10.895, critic_reward: 11.067, revenue_rate: 0.2810, distance: 4.4675, memory: 0.0320, power: 0.1373, lr: 0.000050, took: 63.683s
Epoch 2, Batch 170/3125, loss: 4.163, reward: 10.685, critic_reward: 10.298, revenue_rate: 0.2789, distance: 4.5474, memory: 0.0464, power: 0.1372, lr: 0.000050, took: 62.087s
Epoch 2, Batch 180/3125, loss: 3.957, reward: 10.791, critic_reward: 10.759, revenue_rate: 0.2788, distance: 4.5269, memory: 0.0375, power: 0.1375, lr: 0.000050, took: 62.371s
Epoch 2, Batch 190/3125, loss: 3.515, reward: 10.760, critic_reward: 10.356, revenue_rate: 0.2796, distance: 4.5447, memory: 0.0436, power: 0.1388, lr: 0.000050, took: 62.576s
Epoch 2, Batch 200/3125, loss: 2.925, reward: 10.573, critic_reward: 10.850, revenue_rate: 0.2746, distance: 4.4900, memory: 0.0548, power: 0.1374, lr: 0.000050, took: 61.410s
Epoch 2, Batch 210/3125, loss: 3.480, reward: 11.289, critic_reward: 11.276, revenue_rate: 0.2913, distance: 4.7442, memory: 0.0254, power: 0.1419, lr: 0.000050, took: 64.149s
Epoch 2, Batch 220/3125, loss: 3.697, reward: 10.682, critic_reward: 10.980, revenue_rate: 0.2770, distance: 4.4755, memory: 0.0533, power: 0.1393, lr: 0.000050, took: 62.808s
Epoch 2, Batch 230/3125, loss: 3.306, reward: 10.532, critic_reward: 10.294, revenue_rate: 0.2722, distance: 4.4507, memory: 0.0455, power: 0.1362, lr: 0.000050, took: 63.154s
Epoch 2, Batch 240/3125, loss: 4.240, reward: 10.891, critic_reward: 11.217, revenue_rate: 0.2817, distance: 4.5601, memory: 0.0334, power: 0.1386, lr: 0.000050, took: 62.779s
Epoch 2, Batch 250/3125, loss: 4.168, reward: 11.389, critic_reward: 10.736, revenue_rate: 0.2928, distance: 4.6692, memory: 0.0343, power: 0.1431, lr: 0.000050, took: 63.072s
Epoch 2, Batch 260/3125, loss: 4.035, reward: 10.988, critic_reward: 11.501, revenue_rate: 0.2840, distance: 4.5426, memory: 0.0304, power: 0.1392, lr: 0.000050, took: 63.993s
Epoch 2, Batch 270/3125, loss: 2.619, reward: 10.791, critic_reward: 10.652, revenue_rate: 0.2778, distance: 4.3583, memory: 0.0476, power: 0.1364, lr: 0.000050, took: 62.493s
Epoch 2, Batch 280/3125, loss: 3.662, reward: 11.537, critic_reward: 11.570, revenue_rate: 0.2979, distance: 4.7549, memory: 0.0319, power: 0.1460, lr: 0.000050, took: 66.411s
Epoch 2, Batch 290/3125, loss: 3.303, reward: 10.820, critic_reward: 10.788, revenue_rate: 0.2797, distance: 4.4861, memory: 0.0455, power: 0.1367, lr: 0.000050, took: 62.354s
Epoch 2, Batch 300/3125, loss: 3.420, reward: 11.438, critic_reward: 11.768, revenue_rate: 0.2964, distance: 4.8055, memory: 0.0375, power: 0.1444, lr: 0.000050, took: 64.772s
Epoch 2, Batch 310/3125, loss: 5.574, reward: 11.009, critic_reward: 10.291, revenue_rate: 0.2870, distance: 4.6806, memory: 0.0677, power: 0.1425, lr: 0.000050, took: 65.199s
Epoch 2, Batch 320/3125, loss: 4.657, reward: 11.211, critic_reward: 10.988, revenue_rate: 0.2895, distance: 4.6616, memory: 0.0521, power: 0.1446, lr: 0.000050, took: 64.001s
Epoch 2, Batch 330/3125, loss: 2.965, reward: 10.591, critic_reward: 10.565, revenue_rate: 0.2741, distance: 4.4894, memory: 0.0453, power: 0.1349, lr: 0.000050, took: 62.124s
Epoch 2, Batch 340/3125, loss: 3.685, reward: 10.534, critic_reward: 9.755, revenue_rate: 0.2728, distance: 4.4037, memory: 0.0480, power: 0.1356, lr: 0.000050, took: 61.194s
Epoch 2, Batch 350/3125, loss: 4.568, reward: 11.040, critic_reward: 11.945, revenue_rate: 0.2857, distance: 4.6286, memory: 0.0475, power: 0.1390, lr: 0.000050, took: 62.765s
Epoch 2, Batch 360/3125, loss: 3.967, reward: 10.768, critic_reward: 10.560, revenue_rate: 0.2787, distance: 4.5361, memory: 0.0518, power: 0.1391, lr: 0.000050, took: 62.000s
Epoch 2, Batch 370/3125, loss: 4.318, reward: 10.687, critic_reward: 11.011, revenue_rate: 0.2757, distance: 4.4525, memory: 0.0377, power: 0.1368, lr: 0.000050, took: 61.554s
Epoch 2, Batch 380/3125, loss: 3.821, reward: 10.849, critic_reward: 10.494, revenue_rate: 0.2811, distance: 4.5570, memory: 0.0411, power: 0.1387, lr: 0.000050, took: 62.654s
Epoch 2, Batch 390/3125, loss: 4.716, reward: 10.841, critic_reward: 11.042, revenue_rate: 0.2812, distance: 4.5471, memory: 0.0432, power: 0.1391, lr: 0.000050, took: 62.131s
Epoch 2, Batch 400/3125, loss: 4.897, reward: 10.952, critic_reward: 11.813, revenue_rate: 0.2837, distance: 4.6450, memory: 0.0441, power: 0.1371, lr: 0.000050, took: 62.209s
Epoch 2, Batch 410/3125, loss: 3.812, reward: 11.321, critic_reward: 10.815, revenue_rate: 0.2915, distance: 4.6796, memory: 0.0414, power: 0.1402, lr: 0.000050, took: 63.851s
Epoch 2, Batch 420/3125, loss: 3.149, reward: 10.491, critic_reward: 10.703, revenue_rate: 0.2734, distance: 4.5034, memory: 0.0490, power: 0.1327, lr: 0.000050, took: 64.915s
Epoch 2, Batch 430/3125, loss: 4.805, reward: 10.997, critic_reward: 11.498, revenue_rate: 0.2843, distance: 4.6071, memory: 0.0527, power: 0.1393, lr: 0.000050, took: 61.998s
Epoch 2, Batch 440/3125, loss: 4.146, reward: 11.057, critic_reward: 10.558, revenue_rate: 0.2864, distance: 4.6767, memory: 0.0382, power: 0.1410, lr: 0.000050, took: 62.609s
Epoch 2, Batch 450/3125, loss: 3.745, reward: 10.839, critic_reward: 11.195, revenue_rate: 0.2798, distance: 4.4730, memory: 0.0403, power: 0.1390, lr: 0.000050, took: 62.147s
Epoch 2, Batch 460/3125, loss: 4.114, reward: 10.737, critic_reward: 10.434, revenue_rate: 0.2760, distance: 4.4490, memory: 0.0379, power: 0.1351, lr: 0.000050, took: 63.543s
Epoch 2, Batch 470/3125, loss: 3.686, reward: 10.472, critic_reward: 10.936, revenue_rate: 0.2717, distance: 4.4115, memory: 0.0522, power: 0.1358, lr: 0.000050, took: 61.178s
Epoch 2, Batch 480/3125, loss: 3.980, reward: 11.154, critic_reward: 10.567, revenue_rate: 0.2903, distance: 4.7945, memory: 0.0466, power: 0.1452, lr: 0.000050, took: 63.374s
Epoch 2, Batch 490/3125, loss: 5.260, reward: 11.104, critic_reward: 12.191, revenue_rate: 0.2870, distance: 4.6769, memory: 0.0457, power: 0.1420, lr: 0.000050, took: 62.957s
Epoch 2, Batch 500/3125, loss: 3.329, reward: 11.288, critic_reward: 10.842, revenue_rate: 0.2897, distance: 4.6788, memory: 0.0352, power: 0.1410, lr: 0.000050, took: 64.605s
Epoch 2, Batch 510/3125, loss: 4.007, reward: 10.614, critic_reward: 11.382, revenue_rate: 0.2739, distance: 4.4321, memory: 0.0262, power: 0.1321, lr: 0.000050, took: 60.591s
Epoch 2, Batch 520/3125, loss: 6.102, reward: 11.041, critic_reward: 9.721, revenue_rate: 0.2835, distance: 4.5107, memory: 0.0516, power: 0.1400, lr: 0.000050, took: 61.437s
Epoch 2, Batch 530/3125, loss: 4.013, reward: 11.429, critic_reward: 11.704, revenue_rate: 0.2963, distance: 4.8529, memory: 0.0570, power: 0.1465, lr: 0.000050, took: 64.916s
Epoch 2, Batch 540/3125, loss: 4.774, reward: 11.495, critic_reward: 11.596, revenue_rate: 0.2957, distance: 4.6931, memory: 0.0546, power: 0.1458, lr: 0.000050, took: 63.802s
Epoch 2, Batch 550/3125, loss: 3.393, reward: 11.203, critic_reward: 11.217, revenue_rate: 0.2882, distance: 4.5576, memory: 0.0332, power: 0.1405, lr: 0.000050, took: 61.347s
Epoch 2, Batch 560/3125, loss: 5.365, reward: 11.191, critic_reward: 11.451, revenue_rate: 0.2880, distance: 4.5928, memory: 0.0372, power: 0.1385, lr: 0.000050, took: 65.197s
Epoch 2, Batch 570/3125, loss: 3.658, reward: 11.024, critic_reward: 10.967, revenue_rate: 0.2859, distance: 4.6205, memory: 0.0461, power: 0.1385, lr: 0.000050, took: 63.064s
Epoch 2, Batch 580/3125, loss: 3.844, reward: 10.925, critic_reward: 10.383, revenue_rate: 0.2807, distance: 4.4500, memory: 0.0295, power: 0.1357, lr: 0.000050, took: 61.493s
Epoch 2, Batch 590/3125, loss: 4.112, reward: 10.718, critic_reward: 10.424, revenue_rate: 0.2778, distance: 4.5942, memory: 0.0431, power: 0.1364, lr: 0.000050, took: 61.631s
Epoch 2, Batch 600/3125, loss: 4.642, reward: 11.319, critic_reward: 10.766, revenue_rate: 0.2937, distance: 4.7239, memory: 0.0351, power: 0.1420, lr: 0.000050, took: 63.943s
Epoch 2, Batch 610/3125, loss: 4.822, reward: 10.823, critic_reward: 10.162, revenue_rate: 0.2805, distance: 4.6217, memory: 0.0389, power: 0.1375, lr: 0.000050, took: 62.951s
Epoch 2, Batch 620/3125, loss: 3.807, reward: 10.732, critic_reward: 10.663, revenue_rate: 0.2775, distance: 4.4743, memory: 0.0319, power: 0.1362, lr: 0.000050, took: 63.032s
Epoch 2, Batch 630/3125, loss: 4.120, reward: 10.819, critic_reward: 10.499, revenue_rate: 0.2782, distance: 4.3446, memory: 0.0289, power: 0.1341, lr: 0.000050, took: 60.946s
Epoch 2, Batch 640/3125, loss: 5.080, reward: 11.168, critic_reward: 11.171, revenue_rate: 0.2887, distance: 4.6739, memory: 0.0317, power: 0.1407, lr: 0.000050, took: 62.203s
Epoch 2, Batch 650/3125, loss: 5.371, reward: 11.041, critic_reward: 11.035, revenue_rate: 0.2846, distance: 4.5781, memory: 0.0287, power: 0.1373, lr: 0.000050, took: 61.806s
Epoch 2, Batch 660/3125, loss: 3.552, reward: 11.037, critic_reward: 10.580, revenue_rate: 0.2858, distance: 4.6654, memory: 0.0458, power: 0.1400, lr: 0.000050, took: 63.168s
Epoch 2, Batch 670/3125, loss: 4.381, reward: 11.124, critic_reward: 11.977, revenue_rate: 0.2876, distance: 4.7102, memory: 0.0358, power: 0.1421, lr: 0.000050, took: 62.120s
Epoch 2, Batch 680/3125, loss: 4.467, reward: 11.274, critic_reward: 10.446, revenue_rate: 0.2905, distance: 4.6603, memory: 0.0546, power: 0.1452, lr: 0.000050, took: 63.436s
Epoch 2, Batch 690/3125, loss: 2.912, reward: 10.611, critic_reward: 10.947, revenue_rate: 0.2741, distance: 4.4844, memory: 0.0386, power: 0.1358, lr: 0.000050, took: 62.132s
Epoch 2, Batch 700/3125, loss: 4.397, reward: 11.228, critic_reward: 11.429, revenue_rate: 0.2898, distance: 4.7140, memory: 0.0526, power: 0.1425, lr: 0.000050, took: 63.830s
Epoch 2, Batch 710/3125, loss: 4.654, reward: 11.264, critic_reward: 11.147, revenue_rate: 0.2893, distance: 4.6436, memory: 0.0357, power: 0.1415, lr: 0.000050, took: 65.998s
Epoch 2, Batch 720/3125, loss: 4.238, reward: 11.000, critic_reward: 11.250, revenue_rate: 0.2813, distance: 4.4097, memory: 0.0291, power: 0.1368, lr: 0.000050, took: 61.735s
Epoch 2, Batch 730/3125, loss: 7.474, reward: 11.482, critic_reward: 12.758, revenue_rate: 0.2954, distance: 4.6765, memory: 0.0336, power: 0.1438, lr: 0.000050, took: 64.175s
Epoch 2, Batch 740/3125, loss: 3.891, reward: 11.239, critic_reward: 10.750, revenue_rate: 0.2891, distance: 4.5874, memory: 0.0458, power: 0.1423, lr: 0.000050, took: 63.388s
Epoch 2, Batch 750/3125, loss: 4.448, reward: 11.013, critic_reward: 11.900, revenue_rate: 0.2836, distance: 4.5716, memory: 0.0367, power: 0.1390, lr: 0.000050, took: 61.765s
Epoch 2, Batch 760/3125, loss: 3.895, reward: 10.834, critic_reward: 10.253, revenue_rate: 0.2798, distance: 4.5240, memory: 0.0346, power: 0.1363, lr: 0.000050, took: 62.660s
Epoch 2, Batch 770/3125, loss: 6.434, reward: 11.164, critic_reward: 10.853, revenue_rate: 0.2883, distance: 4.6620, memory: 0.0332, power: 0.1394, lr: 0.000050, took: 63.298s
Epoch 2, Batch 780/3125, loss: 4.655, reward: 11.031, critic_reward: 11.179, revenue_rate: 0.2859, distance: 4.6184, memory: 0.0371, power: 0.1414, lr: 0.000050, took: 63.985s
Epoch 2, Batch 790/3125, loss: 3.889, reward: 11.185, critic_reward: 10.914, revenue_rate: 0.2880, distance: 4.7035, memory: 0.0377, power: 0.1401, lr: 0.000050, took: 63.000s
Epoch 2, Batch 800/3125, loss: 5.103, reward: 11.040, critic_reward: 11.390, revenue_rate: 0.2871, distance: 4.6547, memory: 0.0522, power: 0.1420, lr: 0.000050, took: 63.139s
Epoch 2, Batch 810/3125, loss: 3.578, reward: 10.751, critic_reward: 10.526, revenue_rate: 0.2787, distance: 4.4929, memory: 0.0461, power: 0.1378, lr: 0.000050, took: 63.205s
Epoch 2, Batch 820/3125, loss: 4.137, reward: 10.954, critic_reward: 10.849, revenue_rate: 0.2843, distance: 4.5341, memory: 0.0450, power: 0.1376, lr: 0.000050, took: 62.297s
Epoch 2, Batch 830/3125, loss: 5.645, reward: 11.504, critic_reward: 11.547, revenue_rate: 0.2970, distance: 4.7369, memory: 0.0337, power: 0.1445, lr: 0.000050, took: 64.473s
Epoch 2, Batch 840/3125, loss: 3.101, reward: 10.939, critic_reward: 10.607, revenue_rate: 0.2854, distance: 4.7318, memory: 0.0550, power: 0.1408, lr: 0.000050, took: 64.154s
Epoch 2, Batch 850/3125, loss: 3.952, reward: 10.823, critic_reward: 11.384, revenue_rate: 0.2787, distance: 4.4639, memory: 0.0208, power: 0.1334, lr: 0.000050, took: 65.067s
Epoch 2, Batch 860/3125, loss: 3.793, reward: 11.029, critic_reward: 10.643, revenue_rate: 0.2842, distance: 4.5725, memory: 0.0389, power: 0.1400, lr: 0.000050, took: 63.556s
Epoch 2, Batch 870/3125, loss: 5.404, reward: 10.674, critic_reward: 10.403, revenue_rate: 0.2771, distance: 4.4661, memory: 0.0324, power: 0.1358, lr: 0.000050, took: 61.340s
Epoch 2, Batch 880/3125, loss: 2.575, reward: 10.470, critic_reward: 10.464, revenue_rate: 0.2733, distance: 4.5157, memory: 0.0562, power: 0.1351, lr: 0.000050, took: 61.407s
Epoch 2, Batch 890/3125, loss: 4.539, reward: 11.294, critic_reward: 10.658, revenue_rate: 0.2900, distance: 4.5313, memory: 0.0214, power: 0.1410, lr: 0.000050, took: 62.362s
Epoch 2, Batch 900/3125, loss: 3.771, reward: 10.853, critic_reward: 11.140, revenue_rate: 0.2807, distance: 4.4957, memory: 0.0222, power: 0.1366, lr: 0.000050, took: 62.390s
Epoch 2, Batch 910/3125, loss: 3.653, reward: 10.879, critic_reward: 10.455, revenue_rate: 0.2825, distance: 4.5718, memory: 0.0437, power: 0.1388, lr: 0.000050, took: 63.546s
Epoch 2, Batch 920/3125, loss: 3.108, reward: 11.101, critic_reward: 10.957, revenue_rate: 0.2858, distance: 4.5708, memory: 0.0400, power: 0.1395, lr: 0.000050, took: 62.580s
Epoch 2, Batch 930/3125, loss: 3.655, reward: 10.946, critic_reward: 11.544, revenue_rate: 0.2837, distance: 4.6827, memory: 0.0412, power: 0.1389, lr: 0.000050, took: 62.120s
Epoch 2, Batch 940/3125, loss: 4.599, reward: 10.482, critic_reward: 10.100, revenue_rate: 0.2732, distance: 4.5203, memory: 0.0558, power: 0.1360, lr: 0.000050, took: 61.439s
Epoch 2, Batch 950/3125, loss: 6.245, reward: 10.868, critic_reward: 10.148, revenue_rate: 0.2815, distance: 4.5213, memory: 0.0446, power: 0.1378, lr: 0.000050, took: 61.813s
Epoch 2, Batch 960/3125, loss: 4.508, reward: 10.400, critic_reward: 10.412, revenue_rate: 0.2679, distance: 4.3707, memory: 0.0466, power: 0.1322, lr: 0.000050, took: 60.176s
Epoch 2, Batch 970/3125, loss: 3.393, reward: 10.859, critic_reward: 11.017, revenue_rate: 0.2789, distance: 4.5420, memory: 0.0323, power: 0.1390, lr: 0.000050, took: 61.205s
Epoch 2, Batch 980/3125, loss: 3.438, reward: 11.401, critic_reward: 11.091, revenue_rate: 0.2934, distance: 4.7526, memory: 0.0556, power: 0.1444, lr: 0.000050, took: 64.942s
Epoch 2, Batch 990/3125, loss: 3.159, reward: 10.744, critic_reward: 10.736, revenue_rate: 0.2785, distance: 4.4897, memory: 0.0506, power: 0.1385, lr: 0.000050, took: 64.590s
Epoch 2, Batch 1000/3125, loss: 4.106, reward: 10.926, critic_reward: 10.857, revenue_rate: 0.2823, distance: 4.5282, memory: 0.0265, power: 0.1378, lr: 0.000050, took: 61.816s
Epoch 2, Batch 1010/3125, loss: 5.229, reward: 10.627, critic_reward: 10.849, revenue_rate: 0.2734, distance: 4.3838, memory: 0.0275, power: 0.1327, lr: 0.000050, took: 61.937s
Epoch 2, Batch 1020/3125, loss: 3.537, reward: 10.525, critic_reward: 10.715, revenue_rate: 0.2716, distance: 4.4032, memory: 0.0481, power: 0.1330, lr: 0.000050, took: 62.231s
Epoch 2, Batch 1030/3125, loss: 3.417, reward: 10.676, critic_reward: 10.628, revenue_rate: 0.2761, distance: 4.5463, memory: 0.0496, power: 0.1365, lr: 0.000050, took: 61.744s
Epoch 2, Batch 1040/3125, loss: 3.421, reward: 10.411, critic_reward: 9.730, revenue_rate: 0.2685, distance: 4.2965, memory: 0.0324, power: 0.1311, lr: 0.000050, took: 60.751s
Epoch 2, Batch 1050/3125, loss: 3.444, reward: 10.828, critic_reward: 10.937, revenue_rate: 0.2814, distance: 4.5296, memory: 0.0438, power: 0.1364, lr: 0.000050, took: 62.873s
Epoch 2, Batch 1060/3125, loss: 3.729, reward: 10.453, critic_reward: 10.468, revenue_rate: 0.2695, distance: 4.3077, memory: 0.0311, power: 0.1311, lr: 0.000050, took: 59.684s
Epoch 2, Batch 1070/3125, loss: 5.350, reward: 10.866, critic_reward: 10.135, revenue_rate: 0.2819, distance: 4.5701, memory: 0.0449, power: 0.1363, lr: 0.000050, took: 61.611s
Epoch 2, Batch 1080/3125, loss: 4.499, reward: 11.617, critic_reward: 11.994, revenue_rate: 0.2996, distance: 4.8456, memory: 0.0473, power: 0.1473, lr: 0.000050, took: 62.594s
Epoch 2, Batch 1090/3125, loss: 3.696, reward: 11.282, critic_reward: 11.155, revenue_rate: 0.2903, distance: 4.7043, memory: 0.0466, power: 0.1415, lr: 0.000050, took: 63.572s
Epoch 2, Batch 1100/3125, loss: 5.121, reward: 11.311, critic_reward: 10.670, revenue_rate: 0.2925, distance: 4.7338, memory: 0.0331, power: 0.1433, lr: 0.000050, took: 64.254s
Epoch 2, Batch 1110/3125, loss: 7.908, reward: 11.373, critic_reward: 12.601, revenue_rate: 0.2940, distance: 4.7422, memory: 0.0351, power: 0.1431, lr: 0.000050, took: 62.947s
Epoch 2, Batch 1120/3125, loss: 3.635, reward: 11.023, critic_reward: 10.678, revenue_rate: 0.2857, distance: 4.6175, memory: 0.0225, power: 0.1363, lr: 0.000050, took: 64.012s
Epoch 2, Batch 1130/3125, loss: 2.904, reward: 10.865, critic_reward: 11.120, revenue_rate: 0.2800, distance: 4.4633, memory: 0.0363, power: 0.1382, lr: 0.000050, took: 61.749s
Epoch 2, Batch 1140/3125, loss: 3.244, reward: 10.860, critic_reward: 11.106, revenue_rate: 0.2826, distance: 4.5514, memory: 0.0358, power: 0.1384, lr: 0.000050, took: 63.922s
Epoch 2, Batch 1150/3125, loss: 3.318, reward: 10.829, critic_reward: 10.424, revenue_rate: 0.2826, distance: 4.6223, memory: 0.0570, power: 0.1391, lr: 0.000050, took: 62.827s
Epoch 2, Batch 1160/3125, loss: 3.864, reward: 10.596, critic_reward: 11.044, revenue_rate: 0.2723, distance: 4.4212, memory: 0.0534, power: 0.1354, lr: 0.000050, took: 62.118s
Epoch 2, Batch 1170/3125, loss: 3.530, reward: 10.535, critic_reward: 10.613, revenue_rate: 0.2722, distance: 4.3878, memory: 0.0523, power: 0.1331, lr: 0.000050, took: 59.817s
Epoch 2, Batch 1180/3125, loss: 2.995, reward: 9.946, critic_reward: 10.178, revenue_rate: 0.2601, distance: 4.2860, memory: 0.0749, power: 0.1313, lr: 0.000050, took: 60.574s
Epoch 2, Batch 1190/3125, loss: 3.412, reward: 10.261, critic_reward: 10.827, revenue_rate: 0.2673, distance: 4.4897, memory: 0.0637, power: 0.1350, lr: 0.000050, took: 59.440s
Epoch 2, Batch 1200/3125, loss: 3.389, reward: 9.896, critic_reward: 9.910, revenue_rate: 0.2572, distance: 4.2188, memory: 0.0625, power: 0.1295, lr: 0.000050, took: 57.533s
Epoch 2, Batch 1210/3125, loss: 3.058, reward: 10.003, critic_reward: 9.458, revenue_rate: 0.2594, distance: 4.2568, memory: 0.0634, power: 0.1302, lr: 0.000050, took: 59.423s
Epoch 2, Batch 1220/3125, loss: 2.681, reward: 9.614, critic_reward: 9.490, revenue_rate: 0.2516, distance: 4.1369, memory: 0.0492, power: 0.1245, lr: 0.000050, took: 57.316s
Epoch 2, Batch 1230/3125, loss: 5.018, reward: 10.760, critic_reward: 10.316, revenue_rate: 0.2787, distance: 4.5322, memory: 0.0423, power: 0.1359, lr: 0.000050, took: 58.776s
Epoch 2, Batch 1240/3125, loss: 4.512, reward: 10.621, critic_reward: 10.544, revenue_rate: 0.2731, distance: 4.3536, memory: 0.0481, power: 0.1338, lr: 0.000050, took: 59.186s
Epoch 2, Batch 1250/3125, loss: 3.831, reward: 10.857, critic_reward: 10.740, revenue_rate: 0.2812, distance: 4.4834, memory: 0.0574, power: 0.1383, lr: 0.000050, took: 61.368s
Epoch 2, Batch 1260/3125, loss: 4.218, reward: 10.680, critic_reward: 10.372, revenue_rate: 0.2741, distance: 4.3917, memory: 0.0428, power: 0.1348, lr: 0.000050, took: 59.799s
Epoch 2, Batch 1270/3125, loss: 3.899, reward: 10.267, critic_reward: 10.812, revenue_rate: 0.2671, distance: 4.3344, memory: 0.0531, power: 0.1332, lr: 0.000050, took: 59.164s
Epoch 2, Batch 1280/3125, loss: 2.589, reward: 9.823, critic_reward: 9.578, revenue_rate: 0.2536, distance: 4.1467, memory: 0.0566, power: 0.1254, lr: 0.000050, took: 55.775s
Epoch 2, Batch 1290/3125, loss: 1.947, reward: 8.889, critic_reward: 8.820, revenue_rate: 0.2314, distance: 3.8138, memory: 0.0649, power: 0.1166, lr: 0.000050, took: 53.947s
Epoch 2, Batch 1300/3125, loss: 1.857, reward: 8.373, critic_reward: 8.411, revenue_rate: 0.2186, distance: 3.6299, memory: 0.0602, power: 0.1113, lr: 0.000050, took: 50.836s
Epoch 2, Batch 1310/3125, loss: 2.859, reward: 8.569, critic_reward: 7.940, revenue_rate: 0.2225, distance: 3.7056, memory: 0.0645, power: 0.1118, lr: 0.000050, took: 51.366s
Epoch 2, Batch 1320/3125, loss: 3.377, reward: 9.145, critic_reward: 10.126, revenue_rate: 0.2368, distance: 3.8941, memory: 0.0606, power: 0.1196, lr: 0.000050, took: 52.765s
Epoch 2, Batch 1330/3125, loss: 3.123, reward: 8.926, critic_reward: 7.982, revenue_rate: 0.2337, distance: 3.8503, memory: 0.0563, power: 0.1158, lr: 0.000050, took: 52.214s
Epoch 2, Batch 1340/3125, loss: 2.769, reward: 9.959, critic_reward: 9.753, revenue_rate: 0.2577, distance: 4.1378, memory: 0.0502, power: 0.1276, lr: 0.000050, took: 54.975s
Epoch 2, Batch 1350/3125, loss: 3.292, reward: 9.221, critic_reward: 9.438, revenue_rate: 0.2408, distance: 3.9613, memory: 0.0563, power: 0.1218, lr: 0.000050, took: 54.819s
Epoch 2, Batch 1360/3125, loss: 3.252, reward: 9.385, critic_reward: 9.001, revenue_rate: 0.2442, distance: 4.0748, memory: 0.0747, power: 0.1252, lr: 0.000050, took: 54.357s
Epoch 2, Batch 1370/3125, loss: 3.656, reward: 9.510, critic_reward: 10.080, revenue_rate: 0.2486, distance: 4.1198, memory: 0.0495, power: 0.1208, lr: 0.000050, took: 57.052s
Epoch 2, Batch 1380/3125, loss: 4.184, reward: 9.990, critic_reward: 9.362, revenue_rate: 0.2577, distance: 4.1247, memory: 0.0432, power: 0.1279, lr: 0.000050, took: 56.927s
Epoch 2, Batch 1390/3125, loss: 4.472, reward: 10.086, critic_reward: 10.059, revenue_rate: 0.2625, distance: 4.3209, memory: 0.0544, power: 0.1319, lr: 0.000050, took: 56.575s
Epoch 2, Batch 1400/3125, loss: 3.634, reward: 9.787, critic_reward: 9.502, revenue_rate: 0.2546, distance: 4.1119, memory: 0.0534, power: 0.1258, lr: 0.000050, took: 57.427s
Epoch 2, Batch 1410/3125, loss: 2.961, reward: 9.832, critic_reward: 9.801, revenue_rate: 0.2540, distance: 4.1322, memory: 0.0500, power: 0.1264, lr: 0.000050, took: 56.715s
Epoch 2, Batch 1420/3125, loss: 2.928, reward: 9.606, critic_reward: 9.774, revenue_rate: 0.2502, distance: 4.0181, memory: 0.0391, power: 0.1227, lr: 0.000050, took: 55.581s
Epoch 2, Batch 1430/3125, loss: 4.107, reward: 10.461, critic_reward: 9.692, revenue_rate: 0.2712, distance: 4.4057, memory: 0.0358, power: 0.1320, lr: 0.000050, took: 58.821s
Epoch 2, Batch 1440/3125, loss: 2.707, reward: 10.068, critic_reward: 9.971, revenue_rate: 0.2615, distance: 4.2783, memory: 0.0362, power: 0.1282, lr: 0.000050, took: 59.254s
Epoch 2, Batch 1450/3125, loss: 2.895, reward: 10.690, critic_reward: 10.668, revenue_rate: 0.2758, distance: 4.5210, memory: 0.0505, power: 0.1364, lr: 0.000050, took: 62.882s
Epoch 2, Batch 1460/3125, loss: 3.588, reward: 10.087, critic_reward: 9.931, revenue_rate: 0.2621, distance: 4.1920, memory: 0.0360, power: 0.1291, lr: 0.000050, took: 59.648s
Epoch 2, Batch 1470/3125, loss: 5.451, reward: 10.555, critic_reward: 11.753, revenue_rate: 0.2705, distance: 4.3048, memory: 0.0305, power: 0.1321, lr: 0.000050, took: 59.330s
Epoch 2, Batch 1480/3125, loss: 5.223, reward: 10.233, critic_reward: 9.923, revenue_rate: 0.2649, distance: 4.3089, memory: 0.0381, power: 0.1293, lr: 0.000050, took: 59.959s
Epoch 2, Batch 1490/3125, loss: 3.331, reward: 10.228, critic_reward: 10.413, revenue_rate: 0.2654, distance: 4.3000, memory: 0.0445, power: 0.1313, lr: 0.000050, took: 59.811s
Epoch 2, Batch 1500/3125, loss: 3.202, reward: 10.401, critic_reward: 10.508, revenue_rate: 0.2684, distance: 4.3073, memory: 0.0483, power: 0.1308, lr: 0.000050, took: 58.748s
Epoch 2, Batch 1510/3125, loss: 4.043, reward: 10.196, critic_reward: 10.483, revenue_rate: 0.2627, distance: 4.2703, memory: 0.0450, power: 0.1281, lr: 0.000050, took: 58.637s
Epoch 2, Batch 1520/3125, loss: 2.094, reward: 10.107, critic_reward: 10.310, revenue_rate: 0.2618, distance: 4.2549, memory: 0.0521, power: 0.1283, lr: 0.000050, took: 58.755s
Epoch 2, Batch 1530/3125, loss: 3.531, reward: 9.941, critic_reward: 9.600, revenue_rate: 0.2597, distance: 4.3202, memory: 0.0790, power: 0.1316, lr: 0.000050, took: 56.645s
Epoch 2, Batch 1540/3125, loss: 2.263, reward: 9.753, critic_reward: 9.723, revenue_rate: 0.2524, distance: 4.0953, memory: 0.0451, power: 0.1243, lr: 0.000050, took: 57.308s
Epoch 2, Batch 1550/3125, loss: 2.881, reward: 10.174, critic_reward: 10.121, revenue_rate: 0.2640, distance: 4.3032, memory: 0.0545, power: 0.1314, lr: 0.000050, took: 58.839s
Epoch 2, Batch 1560/3125, loss: 3.320, reward: 10.162, critic_reward: 10.280, revenue_rate: 0.2642, distance: 4.2894, memory: 0.0316, power: 0.1271, lr: 0.000050, took: 58.328s
Epoch 2, Batch 1570/3125, loss: 5.185, reward: 10.402, critic_reward: 9.535, revenue_rate: 0.2699, distance: 4.2637, memory: 0.0507, power: 0.1299, lr: 0.000050, took: 58.646s
Epoch 2, Batch 1580/3125, loss: 7.508, reward: 10.379, critic_reward: 12.214, revenue_rate: 0.2691, distance: 4.3939, memory: 0.0450, power: 0.1326, lr: 0.000050, took: 59.947s
Epoch 2, Batch 1590/3125, loss: 8.496, reward: 10.898, critic_reward: 8.935, revenue_rate: 0.2830, distance: 4.6604, memory: 0.0578, power: 0.1387, lr: 0.000050, took: 60.013s
Epoch 2, Batch 1600/3125, loss: 4.728, reward: 9.668, critic_reward: 10.982, revenue_rate: 0.2514, distance: 4.1244, memory: 0.0451, power: 0.1239, lr: 0.000050, took: 59.556s
Epoch 2, Batch 1610/3125, loss: 2.393, reward: 9.997, critic_reward: 9.861, revenue_rate: 0.2607, distance: 4.3114, memory: 0.0561, power: 0.1298, lr: 0.000050, took: 57.748s
Epoch 2, Batch 1620/3125, loss: 2.950, reward: 10.388, critic_reward: 9.832, revenue_rate: 0.2679, distance: 4.3718, memory: 0.0510, power: 0.1315, lr: 0.000050, took: 59.786s
Epoch 2, Batch 1630/3125, loss: 4.558, reward: 9.935, critic_reward: 11.106, revenue_rate: 0.2593, distance: 4.2392, memory: 0.0462, power: 0.1309, lr: 0.000050, took: 57.385s
Epoch 2, Batch 1640/3125, loss: 5.127, reward: 10.168, critic_reward: 8.875, revenue_rate: 0.2622, distance: 4.1911, memory: 0.0385, power: 0.1285, lr: 0.000050, took: 57.244s
Epoch 2, Batch 1650/3125, loss: 2.891, reward: 10.344, critic_reward: 10.740, revenue_rate: 0.2678, distance: 4.2941, memory: 0.0522, power: 0.1306, lr: 0.000050, took: 58.499s
Epoch 2, Batch 1660/3125, loss: 3.355, reward: 10.288, critic_reward: 9.452, revenue_rate: 0.2668, distance: 4.3411, memory: 0.0487, power: 0.1319, lr: 0.000050, took: 60.005s
Epoch 2, Batch 1670/3125, loss: 5.409, reward: 10.067, critic_reward: 11.569, revenue_rate: 0.2618, distance: 4.2590, memory: 0.0524, power: 0.1291, lr: 0.000050, took: 58.182s
Epoch 2, Batch 1680/3125, loss: 3.194, reward: 10.058, critic_reward: 9.403, revenue_rate: 0.2605, distance: 4.2772, memory: 0.0535, power: 0.1282, lr: 0.000050, took: 58.461s
Epoch 2, Batch 1690/3125, loss: 3.145, reward: 10.074, critic_reward: 9.649, revenue_rate: 0.2606, distance: 4.1212, memory: 0.0344, power: 0.1271, lr: 0.000050, took: 57.288s
Epoch 2, Batch 1700/3125, loss: 3.416, reward: 11.092, critic_reward: 10.917, revenue_rate: 0.2865, distance: 4.6406, memory: 0.0343, power: 0.1398, lr: 0.000050, took: 61.448s
Epoch 2, Batch 1710/3125, loss: 3.520, reward: 10.771, critic_reward: 10.766, revenue_rate: 0.2775, distance: 4.4291, memory: 0.0422, power: 0.1356, lr: 0.000050, took: 61.344s
Epoch 2, Batch 1720/3125, loss: 2.946, reward: 10.777, critic_reward: 10.807, revenue_rate: 0.2802, distance: 4.6131, memory: 0.0463, power: 0.1373, lr: 0.000050, took: 60.190s
Epoch 2, Batch 1730/3125, loss: 3.494, reward: 10.320, critic_reward: 10.107, revenue_rate: 0.2681, distance: 4.3784, memory: 0.0567, power: 0.1344, lr: 0.000050, took: 61.986s
Epoch 2, Batch 1740/3125, loss: 3.531, reward: 10.729, critic_reward: 10.958, revenue_rate: 0.2766, distance: 4.3416, memory: 0.0178, power: 0.1357, lr: 0.000050, took: 61.773s
Epoch 2, Batch 1750/3125, loss: 3.390, reward: 10.716, critic_reward: 10.670, revenue_rate: 0.2787, distance: 4.6233, memory: 0.0477, power: 0.1381, lr: 0.000050, took: 63.005s
Epoch 2, Batch 1760/3125, loss: 4.134, reward: 10.877, critic_reward: 10.714, revenue_rate: 0.2826, distance: 4.6082, memory: 0.0381, power: 0.1373, lr: 0.000050, took: 62.189s
Epoch 2, Batch 1770/3125, loss: 3.257, reward: 10.220, critic_reward: 10.182, revenue_rate: 0.2666, distance: 4.4194, memory: 0.0542, power: 0.1311, lr: 0.000050, took: 61.929s
Epoch 2, Batch 1780/3125, loss: 3.645, reward: 10.759, critic_reward: 10.701, revenue_rate: 0.2782, distance: 4.4500, memory: 0.0441, power: 0.1358, lr: 0.000050, took: 61.791s
Epoch 2, Batch 1790/3125, loss: 2.979, reward: 10.630, critic_reward: 10.600, revenue_rate: 0.2749, distance: 4.4610, memory: 0.0438, power: 0.1349, lr: 0.000050, took: 59.976s
Epoch 2, Batch 1800/3125, loss: 3.702, reward: 11.027, critic_reward: 11.343, revenue_rate: 0.2833, distance: 4.4809, memory: 0.0306, power: 0.1378, lr: 0.000050, took: 61.260s
Epoch 2, Batch 1810/3125, loss: 5.356, reward: 10.805, critic_reward: 9.976, revenue_rate: 0.2805, distance: 4.5006, memory: 0.0490, power: 0.1364, lr: 0.000050, took: 63.479s
Epoch 2, Batch 1820/3125, loss: 3.594, reward: 10.745, critic_reward: 10.568, revenue_rate: 0.2777, distance: 4.4363, memory: 0.0309, power: 0.1369, lr: 0.000050, took: 60.793s
Epoch 2, Batch 1830/3125, loss: 4.231, reward: 10.823, critic_reward: 10.822, revenue_rate: 0.2815, distance: 4.5641, memory: 0.0229, power: 0.1355, lr: 0.000050, took: 61.778s
Epoch 2, Batch 1840/3125, loss: 3.267, reward: 10.519, critic_reward: 10.605, revenue_rate: 0.2732, distance: 4.4669, memory: 0.0476, power: 0.1343, lr: 0.000050, took: 62.585s
Epoch 2, Batch 1850/3125, loss: 2.842, reward: 11.104, critic_reward: 10.965, revenue_rate: 0.2863, distance: 4.6045, memory: 0.0475, power: 0.1411, lr: 0.000050, took: 63.455s
Epoch 2, Batch 1860/3125, loss: 4.586, reward: 10.524, critic_reward: 10.975, revenue_rate: 0.2717, distance: 4.3912, memory: 0.0407, power: 0.1347, lr: 0.000050, took: 61.507s
Epoch 2, Batch 1870/3125, loss: 3.720, reward: 10.935, critic_reward: 10.874, revenue_rate: 0.2824, distance: 4.5501, memory: 0.0517, power: 0.1390, lr: 0.000050, took: 60.966s
Epoch 2, Batch 1880/3125, loss: 3.052, reward: 10.630, critic_reward: 10.458, revenue_rate: 0.2744, distance: 4.4132, memory: 0.0355, power: 0.1341, lr: 0.000050, took: 62.461s
Epoch 2, Batch 1890/3125, loss: 3.233, reward: 10.403, critic_reward: 10.775, revenue_rate: 0.2699, distance: 4.4342, memory: 0.0465, power: 0.1328, lr: 0.000050, took: 61.720s
Epoch 2, Batch 1900/3125, loss: 3.489, reward: 10.800, critic_reward: 10.763, revenue_rate: 0.2796, distance: 4.5501, memory: 0.0507, power: 0.1374, lr: 0.000050, took: 64.767s
Epoch 2, Batch 1910/3125, loss: 3.309, reward: 10.698, critic_reward: 11.020, revenue_rate: 0.2772, distance: 4.4785, memory: 0.0455, power: 0.1360, lr: 0.000050, took: 62.585s
Epoch 2, Batch 1920/3125, loss: 3.279, reward: 10.916, critic_reward: 10.879, revenue_rate: 0.2815, distance: 4.5016, memory: 0.0409, power: 0.1378, lr: 0.000050, took: 64.443s
Epoch 2, Batch 1930/3125, loss: 3.181, reward: 10.458, critic_reward: 10.415, revenue_rate: 0.2712, distance: 4.3643, memory: 0.0377, power: 0.1323, lr: 0.000050, took: 61.130s
Epoch 2, Batch 1940/3125, loss: 3.814, reward: 11.002, critic_reward: 10.432, revenue_rate: 0.2859, distance: 4.5884, memory: 0.0451, power: 0.1389, lr: 0.000050, took: 63.354s
Epoch 2, Batch 1950/3125, loss: 3.315, reward: 10.259, critic_reward: 10.823, revenue_rate: 0.2641, distance: 4.2615, memory: 0.0450, power: 0.1302, lr: 0.000050, took: 59.819s
Epoch 2, Batch 1960/3125, loss: 2.966, reward: 10.253, critic_reward: 10.133, revenue_rate: 0.2657, distance: 4.2528, memory: 0.0500, power: 0.1316, lr: 0.000050, took: 60.546s
Epoch 2, Batch 1970/3125, loss: 2.864, reward: 10.360, critic_reward: 9.971, revenue_rate: 0.2692, distance: 4.3824, memory: 0.0486, power: 0.1318, lr: 0.000050, took: 60.789s
Epoch 2, Batch 1980/3125, loss: 4.072, reward: 10.465, critic_reward: 10.567, revenue_rate: 0.2723, distance: 4.4369, memory: 0.0421, power: 0.1345, lr: 0.000050, took: 60.458s
Epoch 2, Batch 1990/3125, loss: 3.481, reward: 10.561, critic_reward: 10.196, revenue_rate: 0.2726, distance: 4.4077, memory: 0.0461, power: 0.1364, lr: 0.000050, took: 61.097s
Epoch 2, Batch 2000/3125, loss: 3.837, reward: 10.842, critic_reward: 11.265, revenue_rate: 0.2795, distance: 4.6066, memory: 0.0369, power: 0.1367, lr: 0.000050, took: 61.791s
Epoch 2, Batch 2010/3125, loss: 4.879, reward: 10.949, critic_reward: 10.203, revenue_rate: 0.2818, distance: 4.5109, memory: 0.0220, power: 0.1356, lr: 0.000050, took: 62.839s
Epoch 2, Batch 2020/3125, loss: 4.366, reward: 10.661, critic_reward: 10.741, revenue_rate: 0.2754, distance: 4.4375, memory: 0.0389, power: 0.1347, lr: 0.000050, took: 61.919s
Epoch 2, Batch 2030/3125, loss: 4.333, reward: 10.387, critic_reward: 9.867, revenue_rate: 0.2699, distance: 4.3752, memory: 0.0289, power: 0.1321, lr: 0.000050, took: 62.593s
Epoch 2, Batch 2040/3125, loss: 3.529, reward: 10.696, critic_reward: 10.769, revenue_rate: 0.2765, distance: 4.5339, memory: 0.0383, power: 0.1350, lr: 0.000050, took: 64.163s
Epoch 2, Batch 2050/3125, loss: 4.066, reward: 11.101, critic_reward: 11.618, revenue_rate: 0.2876, distance: 4.6416, memory: 0.0436, power: 0.1400, lr: 0.000050, took: 63.378s
Epoch 2, Batch 2060/3125, loss: 3.325, reward: 10.220, critic_reward: 10.036, revenue_rate: 0.2650, distance: 4.2972, memory: 0.0502, power: 0.1309, lr: 0.000050, took: 60.036s
Epoch 2, Batch 2070/3125, loss: 2.965, reward: 10.269, critic_reward: 10.304, revenue_rate: 0.2647, distance: 4.2181, memory: 0.0383, power: 0.1295, lr: 0.000050, took: 60.175s
Epoch 2, Batch 2080/3125, loss: 3.110, reward: 10.338, critic_reward: 10.593, revenue_rate: 0.2682, distance: 4.4362, memory: 0.0460, power: 0.1336, lr: 0.000050, took: 60.940s
Epoch 2, Batch 2090/3125, loss: 3.663, reward: 10.750, critic_reward: 10.713, revenue_rate: 0.2773, distance: 4.3988, memory: 0.0542, power: 0.1352, lr: 0.000050, took: 62.019s
Epoch 2, Batch 2100/3125, loss: 3.110, reward: 10.644, critic_reward: 10.669, revenue_rate: 0.2727, distance: 4.3574, memory: 0.0439, power: 0.1333, lr: 0.000050, took: 61.554s
Epoch 2, Batch 2110/3125, loss: 5.260, reward: 10.781, critic_reward: 9.839, revenue_rate: 0.2774, distance: 4.3780, memory: 0.0425, power: 0.1361, lr: 0.000050, took: 60.020s
Epoch 2, Batch 2120/3125, loss: 3.150, reward: 10.318, critic_reward: 10.409, revenue_rate: 0.2665, distance: 4.3174, memory: 0.0480, power: 0.1309, lr: 0.000050, took: 60.351s
Epoch 2, Batch 2130/3125, loss: 3.544, reward: 10.323, critic_reward: 10.401, revenue_rate: 0.2694, distance: 4.4560, memory: 0.0609, power: 0.1339, lr: 0.000050, took: 62.768s
Epoch 2, Batch 2140/3125, loss: 3.164, reward: 10.192, critic_reward: 10.094, revenue_rate: 0.2638, distance: 4.3534, memory: 0.0417, power: 0.1292, lr: 0.000050, took: 60.989s
Epoch 2, Batch 2150/3125, loss: 3.888, reward: 10.553, critic_reward: 10.216, revenue_rate: 0.2739, distance: 4.4320, memory: 0.0540, power: 0.1349, lr: 0.000050, took: 62.209s
Epoch 2, Batch 2160/3125, loss: 5.031, reward: 10.837, critic_reward: 11.016, revenue_rate: 0.2796, distance: 4.5204, memory: 0.0498, power: 0.1383, lr: 0.000050, took: 65.357s
Epoch 2, Batch 2170/3125, loss: 5.956, reward: 10.765, critic_reward: 10.968, revenue_rate: 0.2786, distance: 4.5237, memory: 0.0346, power: 0.1357, lr: 0.000050, took: 62.747s
Epoch 2, Batch 2180/3125, loss: 4.145, reward: 10.787, critic_reward: 10.277, revenue_rate: 0.2774, distance: 4.3939, memory: 0.0436, power: 0.1370, lr: 0.000050, took: 62.380s
Epoch 2, Batch 2190/3125, loss: 3.994, reward: 11.121, critic_reward: 11.043, revenue_rate: 0.2872, distance: 4.5364, memory: 0.0462, power: 0.1400, lr: 0.000050, took: 64.013s
Epoch 2, Batch 2200/3125, loss: 3.731, reward: 10.457, critic_reward: 10.289, revenue_rate: 0.2694, distance: 4.3494, memory: 0.0325, power: 0.1318, lr: 0.000050, took: 61.153s
Epoch 2, Batch 2210/3125, loss: 2.966, reward: 10.633, critic_reward: 10.633, revenue_rate: 0.2760, distance: 4.4857, memory: 0.0540, power: 0.1343, lr: 0.000050, took: 59.970s
Epoch 2, Batch 2220/3125, loss: 3.153, reward: 10.573, critic_reward: 10.511, revenue_rate: 0.2719, distance: 4.4159, memory: 0.0540, power: 0.1328, lr: 0.000050, took: 60.811s
Epoch 2, Batch 2230/3125, loss: 3.121, reward: 10.517, critic_reward: 10.844, revenue_rate: 0.2722, distance: 4.4267, memory: 0.0334, power: 0.1333, lr: 0.000050, took: 59.553s
Epoch 2, Batch 2240/3125, loss: 2.612, reward: 10.622, critic_reward: 10.492, revenue_rate: 0.2741, distance: 4.4202, memory: 0.0569, power: 0.1351, lr: 0.000050, took: 62.077s
Epoch 2, Batch 2250/3125, loss: 2.737, reward: 10.782, critic_reward: 11.036, revenue_rate: 0.2782, distance: 4.4942, memory: 0.0460, power: 0.1394, lr: 0.000050, took: 62.096s
Epoch 2, Batch 2260/3125, loss: 3.831, reward: 11.087, critic_reward: 10.898, revenue_rate: 0.2874, distance: 4.6349, memory: 0.0462, power: 0.1411, lr: 0.000050, took: 64.395s
Epoch 2, Batch 2270/3125, loss: 3.380, reward: 11.028, critic_reward: 11.160, revenue_rate: 0.2855, distance: 4.6216, memory: 0.0365, power: 0.1408, lr: 0.000050, took: 60.376s
Epoch 2, Batch 2280/3125, loss: 3.238, reward: 10.858, critic_reward: 10.958, revenue_rate: 0.2798, distance: 4.4971, memory: 0.0466, power: 0.1354, lr: 0.000050, took: 60.167s
Epoch 2, Batch 2290/3125, loss: 4.012, reward: 11.407, critic_reward: 10.611, revenue_rate: 0.2958, distance: 4.7719, memory: 0.0501, power: 0.1453, lr: 0.000050, took: 62.861s
Epoch 2, Batch 2300/3125, loss: 5.458, reward: 10.665, critic_reward: 12.085, revenue_rate: 0.2774, distance: 4.5242, memory: 0.0610, power: 0.1381, lr: 0.000050, took: 62.712s
Epoch 2, Batch 2310/3125, loss: 3.874, reward: 10.457, critic_reward: 9.580, revenue_rate: 0.2734, distance: 4.5810, memory: 0.0584, power: 0.1381, lr: 0.000050, took: 60.003s
Epoch 2, Batch 2320/3125, loss: 2.873, reward: 10.033, critic_reward: 10.296, revenue_rate: 0.2610, distance: 4.2709, memory: 0.0470, power: 0.1304, lr: 0.000050, took: 58.779s
Epoch 2, Batch 2330/3125, loss: 5.011, reward: 10.680, critic_reward: 10.706, revenue_rate: 0.2767, distance: 4.4384, memory: 0.0540, power: 0.1383, lr: 0.000050, took: 59.351s
Epoch 2, Batch 2340/3125, loss: 5.131, reward: 10.199, critic_reward: 11.772, revenue_rate: 0.2651, distance: 4.3141, memory: 0.0687, power: 0.1332, lr: 0.000050, took: 61.018s
Epoch 2, Batch 2350/3125, loss: 5.146, reward: 10.073, critic_reward: 8.550, revenue_rate: 0.2620, distance: 4.2732, memory: 0.0612, power: 0.1302, lr: 0.000050, took: 59.774s
Epoch 2, Batch 2360/3125, loss: 3.476, reward: 10.612, critic_reward: 10.689, revenue_rate: 0.2773, distance: 4.5149, memory: 0.0629, power: 0.1379, lr: 0.000050, took: 60.749s
Epoch 2, Batch 2370/3125, loss: 3.355, reward: 10.373, critic_reward: 10.366, revenue_rate: 0.2685, distance: 4.3868, memory: 0.0565, power: 0.1325, lr: 0.000050, took: 60.884s
Epoch 2, Batch 2380/3125, loss: 3.688, reward: 10.581, critic_reward: 10.339, revenue_rate: 0.2732, distance: 4.4134, memory: 0.0514, power: 0.1360, lr: 0.000050, took: 59.743s
Epoch 2, Batch 2390/3125, loss: 3.460, reward: 10.557, critic_reward: 10.945, revenue_rate: 0.2744, distance: 4.4355, memory: 0.0563, power: 0.1339, lr: 0.000050, took: 60.911s
Epoch 2, Batch 2400/3125, loss: 5.303, reward: 10.358, critic_reward: 9.107, revenue_rate: 0.2687, distance: 4.3666, memory: 0.0621, power: 0.1326, lr: 0.000050, took: 59.188s
Epoch 2, Batch 2410/3125, loss: 2.932, reward: 10.482, critic_reward: 11.098, revenue_rate: 0.2710, distance: 4.4821, memory: 0.0681, power: 0.1353, lr: 0.000050, took: 60.194s
Epoch 2, Batch 2420/3125, loss: 5.665, reward: 9.870, critic_reward: 8.353, revenue_rate: 0.2542, distance: 4.1329, memory: 0.0522, power: 0.1273, lr: 0.000050, took: 56.577s
Epoch 2, Batch 2430/3125, loss: 3.716, reward: 9.932, critic_reward: 10.296, revenue_rate: 0.2575, distance: 4.1846, memory: 0.0539, power: 0.1261, lr: 0.000050, took: 59.073s
Epoch 2, Batch 2440/3125, loss: 3.063, reward: 9.998, critic_reward: 10.465, revenue_rate: 0.2599, distance: 4.2724, memory: 0.0752, power: 0.1308, lr: 0.000050, took: 60.274s
Epoch 2, Batch 2450/3125, loss: 3.989, reward: 10.093, critic_reward: 10.472, revenue_rate: 0.2584, distance: 4.1995, memory: 0.0613, power: 0.1290, lr: 0.000050, took: 59.279s
Epoch 2, Batch 2460/3125, loss: 4.110, reward: 10.902, critic_reward: 10.099, revenue_rate: 0.2808, distance: 4.6329, memory: 0.0691, power: 0.1411, lr: 0.000050, took: 62.293s
Epoch 2, Batch 2470/3125, loss: 3.263, reward: 9.865, critic_reward: 10.643, revenue_rate: 0.2563, distance: 4.2195, memory: 0.0602, power: 0.1280, lr: 0.000050, took: 57.384s
Epoch 2, Batch 2480/3125, loss: 4.647, reward: 10.204, critic_reward: 8.931, revenue_rate: 0.2645, distance: 4.3124, memory: 0.0655, power: 0.1322, lr: 0.000050, took: 60.842s
Epoch 2, Batch 2490/3125, loss: 3.217, reward: 10.592, critic_reward: 10.845, revenue_rate: 0.2753, distance: 4.5075, memory: 0.0420, power: 0.1365, lr: 0.000050, took: 61.327s
Epoch 2, Batch 2500/3125, loss: 2.639, reward: 10.232, critic_reward: 9.808, revenue_rate: 0.2659, distance: 4.3662, memory: 0.0613, power: 0.1323, lr: 0.000050, took: 61.449s
Epoch 2, Batch 2510/3125, loss: 4.019, reward: 10.390, critic_reward: 10.726, revenue_rate: 0.2698, distance: 4.4313, memory: 0.0599, power: 0.1336, lr: 0.000050, took: 61.909s
Epoch 2, Batch 2520/3125, loss: 4.524, reward: 10.360, critic_reward: 9.756, revenue_rate: 0.2697, distance: 4.4305, memory: 0.0479, power: 0.1319, lr: 0.000050, took: 60.610s
Epoch 2, Batch 2530/3125, loss: 4.453, reward: 10.265, critic_reward: 10.407, revenue_rate: 0.2674, distance: 4.4203, memory: 0.0638, power: 0.1342, lr: 0.000050, took: 60.752s
Epoch 2, Batch 2540/3125, loss: 4.807, reward: 11.034, critic_reward: 10.102, revenue_rate: 0.2836, distance: 4.4906, memory: 0.0342, power: 0.1394, lr: 0.000050, took: 60.626s
Epoch 2, Batch 2550/3125, loss: 3.973, reward: 10.951, critic_reward: 11.486, revenue_rate: 0.2834, distance: 4.5335, memory: 0.0491, power: 0.1376, lr: 0.000050, took: 61.631s
Epoch 2, Batch 2560/3125, loss: 2.698, reward: 10.453, critic_reward: 10.089, revenue_rate: 0.2701, distance: 4.3750, memory: 0.0534, power: 0.1335, lr: 0.000050, took: 60.112s
Epoch 2, Batch 2570/3125, loss: 3.494, reward: 11.184, critic_reward: 11.514, revenue_rate: 0.2910, distance: 4.7884, memory: 0.0574, power: 0.1453, lr: 0.000050, took: 62.339s
Epoch 2, Batch 2580/3125, loss: 3.239, reward: 10.849, critic_reward: 11.192, revenue_rate: 0.2807, distance: 4.4842, memory: 0.0370, power: 0.1389, lr: 0.000050, took: 62.197s
Epoch 2, Batch 2590/3125, loss: 4.425, reward: 10.857, critic_reward: 10.370, revenue_rate: 0.2811, distance: 4.5607, memory: 0.0429, power: 0.1384, lr: 0.000050, took: 62.185s
Epoch 2, Batch 2600/3125, loss: 3.994, reward: 10.890, critic_reward: 11.539, revenue_rate: 0.2802, distance: 4.4427, memory: 0.0340, power: 0.1380, lr: 0.000050, took: 63.767s
Epoch 2, Batch 2610/3125, loss: 4.006, reward: 11.001, critic_reward: 10.657, revenue_rate: 0.2830, distance: 4.5502, memory: 0.0510, power: 0.1404, lr: 0.000050, took: 63.596s
Epoch 2, Batch 2620/3125, loss: 3.149, reward: 10.487, critic_reward: 10.823, revenue_rate: 0.2721, distance: 4.3809, memory: 0.0509, power: 0.1331, lr: 0.000050, took: 61.979s
Epoch 2, Batch 2630/3125, loss: 2.861, reward: 10.827, critic_reward: 10.441, revenue_rate: 0.2804, distance: 4.5619, memory: 0.0456, power: 0.1371, lr: 0.000050, took: 66.002s
Epoch 2, Batch 2640/3125, loss: 3.789, reward: 10.671, critic_reward: 10.587, revenue_rate: 0.2760, distance: 4.5473, memory: 0.0462, power: 0.1354, lr: 0.000050, took: 61.961s
Epoch 2, Batch 2650/3125, loss: 4.525, reward: 10.694, critic_reward: 11.268, revenue_rate: 0.2774, distance: 4.5785, memory: 0.0645, power: 0.1381, lr: 0.000050, took: 61.798s
Epoch 2, Batch 2660/3125, loss: 4.445, reward: 11.072, critic_reward: 10.607, revenue_rate: 0.2839, distance: 4.4568, memory: 0.0229, power: 0.1379, lr: 0.000050, took: 62.799s
Epoch 2, Batch 2670/3125, loss: 4.246, reward: 10.840, critic_reward: 10.255, revenue_rate: 0.2819, distance: 4.5971, memory: 0.0533, power: 0.1386, lr: 0.000050, took: 64.337s
Epoch 2, Batch 2680/3125, loss: 3.440, reward: 10.833, critic_reward: 11.295, revenue_rate: 0.2804, distance: 4.5404, memory: 0.0388, power: 0.1388, lr: 0.000050, took: 63.197s
Epoch 2, Batch 2690/3125, loss: 3.477, reward: 10.364, critic_reward: 10.078, revenue_rate: 0.2665, distance: 4.2812, memory: 0.0353, power: 0.1294, lr: 0.000050, took: 61.989s
Epoch 2, Batch 2700/3125, loss: 3.456, reward: 10.340, critic_reward: 11.105, revenue_rate: 0.2670, distance: 4.2546, memory: 0.0358, power: 0.1283, lr: 0.000050, took: 60.963s
Epoch 2, Batch 2710/3125, loss: 5.401, reward: 10.690, critic_reward: 10.093, revenue_rate: 0.2762, distance: 4.4914, memory: 0.0497, power: 0.1376, lr: 0.000050, took: 63.621s
Epoch 2, Batch 2720/3125, loss: 3.077, reward: 10.028, critic_reward: 10.599, revenue_rate: 0.2616, distance: 4.3778, memory: 0.0561, power: 0.1315, lr: 0.000050, took: 63.375s
Epoch 2, Batch 2730/3125, loss: 6.923, reward: 10.772, critic_reward: 9.186, revenue_rate: 0.2800, distance: 4.5650, memory: 0.0393, power: 0.1374, lr: 0.000050, took: 63.188s
Epoch 2, Batch 2740/3125, loss: 3.260, reward: 10.765, critic_reward: 11.466, revenue_rate: 0.2795, distance: 4.4990, memory: 0.0490, power: 0.1394, lr: 0.000050, took: 64.008s
Epoch 2, Batch 2750/3125, loss: 3.597, reward: 10.884, critic_reward: 10.151, revenue_rate: 0.2803, distance: 4.5354, memory: 0.0450, power: 0.1371, lr: 0.000050, took: 63.544s
Epoch 2, Batch 2760/3125, loss: 3.437, reward: 10.335, critic_reward: 11.389, revenue_rate: 0.2671, distance: 4.2439, memory: 0.0357, power: 0.1307, lr: 0.000050, took: 63.595s
Epoch 2, Batch 2770/3125, loss: 6.071, reward: 10.853, critic_reward: 9.437, revenue_rate: 0.2806, distance: 4.4557, memory: 0.0312, power: 0.1368, lr: 0.000050, took: 66.129s
Epoch 2, Batch 2780/3125, loss: 3.771, reward: 10.712, critic_reward: 11.440, revenue_rate: 0.2769, distance: 4.5291, memory: 0.0508, power: 0.1372, lr: 0.000050, took: 63.576s
Epoch 2, Batch 2790/3125, loss: 4.224, reward: 11.036, critic_reward: 10.484, revenue_rate: 0.2843, distance: 4.5144, memory: 0.0240, power: 0.1368, lr: 0.000050, took: 63.232s
Epoch 2, Batch 2800/3125, loss: 3.459, reward: 10.477, critic_reward: 9.944, revenue_rate: 0.2703, distance: 4.3507, memory: 0.0539, power: 0.1346, lr: 0.000050, took: 63.588s
Epoch 2, Batch 2810/3125, loss: 3.314, reward: 10.717, critic_reward: 11.392, revenue_rate: 0.2788, distance: 4.5734, memory: 0.0515, power: 0.1380, lr: 0.000050, took: 64.719s
Epoch 2, Batch 2820/3125, loss: 3.342, reward: 10.445, critic_reward: 10.387, revenue_rate: 0.2703, distance: 4.3749, memory: 0.0399, power: 0.1322, lr: 0.000050, took: 64.193s
Epoch 2, Batch 2830/3125, loss: 4.473, reward: 10.917, critic_reward: 11.659, revenue_rate: 0.2826, distance: 4.4903, memory: 0.0373, power: 0.1363, lr: 0.000050, took: 64.768s
Epoch 2, Batch 2840/3125, loss: 3.021, reward: 10.635, critic_reward: 10.310, revenue_rate: 0.2756, distance: 4.4967, memory: 0.0443, power: 0.1362, lr: 0.000050, took: 63.182s
Epoch 2, Batch 2850/3125, loss: 2.841, reward: 10.850, critic_reward: 10.526, revenue_rate: 0.2820, distance: 4.5974, memory: 0.0513, power: 0.1394, lr: 0.000050, took: 65.185s
Epoch 2, Batch 2860/3125, loss: 3.182, reward: 10.969, critic_reward: 10.886, revenue_rate: 0.2829, distance: 4.5107, memory: 0.0406, power: 0.1406, lr: 0.000050, took: 64.379s
Epoch 2, Batch 2870/3125, loss: 3.723, reward: 11.046, critic_reward: 11.060, revenue_rate: 0.2835, distance: 4.4971, memory: 0.0315, power: 0.1393, lr: 0.000050, took: 60.963s
Epoch 2, Batch 2880/3125, loss: 3.965, reward: 10.840, critic_reward: 10.046, revenue_rate: 0.2808, distance: 4.5678, memory: 0.0521, power: 0.1371, lr: 0.000050, took: 62.697s
Epoch 2, Batch 2890/3125, loss: 3.646, reward: 10.853, critic_reward: 11.353, revenue_rate: 0.2811, distance: 4.5780, memory: 0.0353, power: 0.1380, lr: 0.000050, took: 61.172s
Epoch 2, Batch 2900/3125, loss: 2.837, reward: 11.101, critic_reward: 10.921, revenue_rate: 0.2857, distance: 4.5364, memory: 0.0409, power: 0.1375, lr: 0.000050, took: 63.117s
Epoch 2, Batch 2910/3125, loss: 4.104, reward: 10.623, critic_reward: 10.010, revenue_rate: 0.2757, distance: 4.4848, memory: 0.0284, power: 0.1349, lr: 0.000050, took: 60.840s
Epoch 2, Batch 2920/3125, loss: 5.988, reward: 11.498, critic_reward: 12.707, revenue_rate: 0.2945, distance: 4.6951, memory: 0.0324, power: 0.1431, lr: 0.000050, took: 64.583s
Epoch 2, Batch 2930/3125, loss: 5.392, reward: 10.870, critic_reward: 11.205, revenue_rate: 0.2825, distance: 4.6180, memory: 0.0396, power: 0.1383, lr: 0.000050, took: 63.090s
Epoch 2, Batch 2940/3125, loss: 5.691, reward: 11.294, critic_reward: 10.636, revenue_rate: 0.2933, distance: 4.7891, memory: 0.0613, power: 0.1487, lr: 0.000050, took: 63.766s
Epoch 2, Batch 2950/3125, loss: 2.431, reward: 10.732, critic_reward: 10.866, revenue_rate: 0.2779, distance: 4.4681, memory: 0.0520, power: 0.1371, lr: 0.000050, took: 63.045s
Epoch 2, Batch 2960/3125, loss: 5.547, reward: 11.093, critic_reward: 9.706, revenue_rate: 0.2886, distance: 4.6362, memory: 0.0303, power: 0.1398, lr: 0.000050, took: 63.321s
Epoch 2, Batch 2970/3125, loss: 3.783, reward: 10.821, critic_reward: 11.007, revenue_rate: 0.2792, distance: 4.4194, memory: 0.0130, power: 0.1333, lr: 0.000050, took: 61.631s
Epoch 2, Batch 2980/3125, loss: 3.654, reward: 10.980, critic_reward: 11.134, revenue_rate: 0.2850, distance: 4.5330, memory: 0.0361, power: 0.1392, lr: 0.000050, took: 63.125s
Epoch 2, Batch 2990/3125, loss: 4.328, reward: 10.311, critic_reward: 10.576, revenue_rate: 0.2679, distance: 4.3960, memory: 0.0600, power: 0.1350, lr: 0.000050, took: 61.252s
Epoch 2, Batch 3000/3125, loss: 2.809, reward: 10.326, critic_reward: 10.041, revenue_rate: 0.2672, distance: 4.2992, memory: 0.0413, power: 0.1316, lr: 0.000050, took: 62.183s
Epoch 2, Batch 3010/3125, loss: 4.658, reward: 10.696, critic_reward: 11.649, revenue_rate: 0.2757, distance: 4.3974, memory: 0.0278, power: 0.1344, lr: 0.000050, took: 62.012s
Epoch 2, Batch 3020/3125, loss: 2.844, reward: 10.811, critic_reward: 10.658, revenue_rate: 0.2813, distance: 4.6180, memory: 0.0541, power: 0.1407, lr: 0.000050, took: 62.707s
Epoch 2, Batch 3030/3125, loss: 4.631, reward: 11.053, critic_reward: 10.838, revenue_rate: 0.2835, distance: 4.5276, memory: 0.0308, power: 0.1393, lr: 0.000050, took: 62.781s
Epoch 2, Batch 3040/3125, loss: 3.243, reward: 11.399, critic_reward: 11.042, revenue_rate: 0.2918, distance: 4.5337, memory: 0.0243, power: 0.1412, lr: 0.000050, took: 62.395s
Epoch 2, Batch 3050/3125, loss: 5.317, reward: 11.450, critic_reward: 12.474, revenue_rate: 0.2954, distance: 4.7558, memory: 0.0407, power: 0.1433, lr: 0.000050, took: 62.130s
Epoch 2, Batch 3060/3125, loss: 3.271, reward: 10.887, critic_reward: 10.626, revenue_rate: 0.2821, distance: 4.5452, memory: 0.0531, power: 0.1406, lr: 0.000050, took: 64.604s
Epoch 2, Batch 3070/3125, loss: 2.666, reward: 10.365, critic_reward: 10.558, revenue_rate: 0.2682, distance: 4.3685, memory: 0.0401, power: 0.1320, lr: 0.000050, took: 61.207s
Epoch 2, Batch 3080/3125, loss: 3.197, reward: 10.652, critic_reward: 10.515, revenue_rate: 0.2746, distance: 4.4151, memory: 0.0491, power: 0.1363, lr: 0.000050, took: 62.935s
Epoch 2, Batch 3090/3125, loss: 3.373, reward: 11.416, critic_reward: 10.931, revenue_rate: 0.2921, distance: 4.6754, memory: 0.0374, power: 0.1432, lr: 0.000050, took: 63.598s
Epoch 2, Batch 3100/3125, loss: 4.445, reward: 11.192, critic_reward: 11.086, revenue_rate: 0.2898, distance: 4.6922, memory: 0.0392, power: 0.1420, lr: 0.000050, took: 63.475s
Epoch 2, Batch 3110/3125, loss: 3.898, reward: 10.653, critic_reward: 11.272, revenue_rate: 0.2753, distance: 4.4409, memory: 0.0403, power: 0.1350, lr: 0.000050, took: 62.384s
Epoch 2, Batch 3120/3125, loss: 4.072, reward: 11.202, critic_reward: 11.098, revenue_rate: 0.2882, distance: 4.6151, memory: 0.0370, power: 0.1394, lr: 0.000050, took: 65.333s
开始验证...
Test Batch 0/313, reward: 11.030, revenue_rate: 0.2847, distance: 4.5472, memory: 0.0379, power: 0.1365
Test Batch 1/313, reward: 11.072, revenue_rate: 0.2903, distance: 4.8659, memory: 0.0636, power: 0.1459
Test Batch 2/313, reward: 11.388, revenue_rate: 0.2948, distance: 4.9701, memory: 0.0713, power: 0.1520
Test Batch 3/313, reward: 11.672, revenue_rate: 0.3048, distance: 4.9045, memory: 0.0483, power: 0.1524
Test Batch 4/313, reward: 9.668, revenue_rate: 0.2504, distance: 3.9302, memory: -0.0033, power: 0.1185
Test Batch 5/313, reward: 9.842, revenue_rate: 0.2566, distance: 4.1748, memory: 0.0304, power: 0.1287
Test Batch 6/313, reward: 11.364, revenue_rate: 0.2995, distance: 4.8618, memory: 0.0561, power: 0.1546
Test Batch 7/313, reward: 11.209, revenue_rate: 0.2959, distance: 5.0506, memory: 0.0841, power: 0.1533
Test Batch 8/313, reward: 10.688, revenue_rate: 0.2748, distance: 4.4540, memory: 0.0357, power: 0.1349
Test Batch 9/313, reward: 11.472, revenue_rate: 0.2995, distance: 4.9172, memory: 0.1015, power: 0.1555
Test Batch 10/313, reward: 12.291, revenue_rate: 0.3104, distance: 4.9179, memory: 0.0355, power: 0.1465
Test Batch 11/313, reward: 11.381, revenue_rate: 0.2911, distance: 4.4834, memory: 0.0685, power: 0.1427
Test Batch 12/313, reward: 10.776, revenue_rate: 0.2736, distance: 4.1705, memory: 0.0038, power: 0.1290
Test Batch 13/313, reward: 12.651, revenue_rate: 0.3297, distance: 5.2190, memory: 0.0564, power: 0.1598
Test Batch 14/313, reward: 11.125, revenue_rate: 0.2895, distance: 4.7290, memory: 0.0299, power: 0.1475
Test Batch 15/313, reward: 10.065, revenue_rate: 0.2643, distance: 4.5007, memory: 0.0501, power: 0.1320
Test Batch 16/313, reward: 10.179, revenue_rate: 0.2597, distance: 4.0733, memory: 0.0326, power: 0.1232
Test Batch 17/313, reward: 10.299, revenue_rate: 0.2748, distance: 4.5513, memory: 0.0598, power: 0.1344
Test Batch 18/313, reward: 12.271, revenue_rate: 0.3119, distance: 5.0801, memory: 0.0776, power: 0.1556
Test Batch 19/313, reward: 11.577, revenue_rate: 0.3004, distance: 4.7356, memory: 0.0539, power: 0.1441
Test Batch 20/313, reward: 9.997, revenue_rate: 0.2618, distance: 4.2334, memory: 0.0628, power: 0.1278
Test Batch 21/313, reward: 10.100, revenue_rate: 0.2703, distance: 4.6791, memory: 0.0463, power: 0.1312
Test Batch 22/313, reward: 10.730, revenue_rate: 0.2725, distance: 4.3912, memory: -0.0296, power: 0.1290
Test Batch 23/313, reward: 11.388, revenue_rate: 0.2974, distance: 5.2275, memory: 0.0304, power: 0.1415
Test Batch 24/313, reward: 10.533, revenue_rate: 0.2758, distance: 4.4358, memory: 0.0424, power: 0.1376
Test Batch 25/313, reward: 13.393, revenue_rate: 0.3388, distance: 5.3014, memory: -0.0077, power: 0.1580
Test Batch 26/313, reward: 11.654, revenue_rate: 0.3011, distance: 4.9209, memory: 0.0618, power: 0.1396
Test Batch 27/313, reward: 11.297, revenue_rate: 0.2920, distance: 4.4996, memory: 0.0561, power: 0.1385
Test Batch 28/313, reward: 9.958, revenue_rate: 0.2548, distance: 3.8851, memory: 0.0597, power: 0.1210
Test Batch 29/313, reward: 10.664, revenue_rate: 0.2787, distance: 4.5945, memory: 0.0314, power: 0.1327
Test Batch 30/313, reward: 10.616, revenue_rate: 0.2723, distance: 4.2533, memory: 0.0286, power: 0.1420
Test Batch 31/313, reward: 12.347, revenue_rate: 0.3167, distance: 5.0455, memory: 0.0249, power: 0.1547
Test Batch 32/313, reward: 10.588, revenue_rate: 0.2742, distance: 4.6489, memory: 0.0527, power: 0.1289
Test Batch 33/313, reward: 11.098, revenue_rate: 0.2825, distance: 4.4706, memory: 0.0237, power: 0.1343
Test Batch 34/313, reward: 12.325, revenue_rate: 0.3154, distance: 5.1026, memory: 0.1117, power: 0.1542
Test Batch 35/313, reward: 11.558, revenue_rate: 0.2978, distance: 4.4998, memory: -0.0011, power: 0.1412
Test Batch 36/313, reward: 12.402, revenue_rate: 0.3204, distance: 5.1575, memory: 0.0650, power: 0.1605
Test Batch 37/313, reward: 10.428, revenue_rate: 0.2692, distance: 4.4426, memory: 0.0704, power: 0.1376
Test Batch 38/313, reward: 10.759, revenue_rate: 0.2779, distance: 4.3371, memory: -0.0058, power: 0.1248
Test Batch 39/313, reward: 10.822, revenue_rate: 0.2784, distance: 4.4340, memory: 0.0248, power: 0.1352
Test Batch 40/313, reward: 9.961, revenue_rate: 0.2556, distance: 4.0286, memory: 0.0098, power: 0.1275
Test Batch 41/313, reward: 10.103, revenue_rate: 0.2661, distance: 4.2254, memory: 0.0527, power: 0.1276
Test Batch 42/313, reward: 10.269, revenue_rate: 0.2677, distance: 4.3657, memory: 0.0692, power: 0.1342
Test Batch 43/313, reward: 11.474, revenue_rate: 0.2913, distance: 4.5982, memory: 0.0000, power: 0.1421
Test Batch 44/313, reward: 11.139, revenue_rate: 0.2875, distance: 4.4913, memory: -0.0449, power: 0.1309
Test Batch 45/313, reward: 11.553, revenue_rate: 0.3018, distance: 4.8139, memory: 0.0374, power: 0.1490
Test Batch 46/313, reward: 10.689, revenue_rate: 0.2823, distance: 4.5054, memory: 0.0405, power: 0.1379
Test Batch 47/313, reward: 12.297, revenue_rate: 0.3169, distance: 5.1160, memory: 0.0674, power: 0.1602
Test Batch 48/313, reward: 11.216, revenue_rate: 0.2922, distance: 4.7604, memory: 0.0248, power: 0.1400
Test Batch 49/313, reward: 12.197, revenue_rate: 0.3186, distance: 5.1546, memory: 0.0042, power: 0.1592
Test Batch 50/313, reward: 10.162, revenue_rate: 0.2613, distance: 4.0829, memory: -0.0058, power: 0.1240
Test Batch 51/313, reward: 11.482, revenue_rate: 0.2859, distance: 4.2367, memory: 0.0023, power: 0.1339
Test Batch 52/313, reward: 11.480, revenue_rate: 0.2987, distance: 5.0198, memory: 0.0788, power: 0.1539
Test Batch 53/313, reward: 10.754, revenue_rate: 0.2789, distance: 4.5657, memory: 0.0249, power: 0.1336
Test Batch 54/313, reward: 10.928, revenue_rate: 0.2827, distance: 4.5765, memory: 0.0263, power: 0.1353
Test Batch 55/313, reward: 11.448, revenue_rate: 0.2993, distance: 4.8933, memory: 0.0594, power: 0.1454
Test Batch 56/313, reward: 10.557, revenue_rate: 0.2649, distance: 3.9407, memory: 0.0093, power: 0.1238
Test Batch 57/313, reward: 10.314, revenue_rate: 0.2675, distance: 4.2052, memory: 0.0396, power: 0.1278
Test Batch 58/313, reward: 11.680, revenue_rate: 0.3003, distance: 4.8892, memory: 0.0410, power: 0.1473
Test Batch 59/313, reward: 9.970, revenue_rate: 0.2622, distance: 4.4497, memory: 0.0740, power: 0.1309
Test Batch 60/313, reward: 10.356, revenue_rate: 0.2715, distance: 4.4385, memory: 0.0585, power: 0.1354
Test Batch 61/313, reward: 10.437, revenue_rate: 0.2755, distance: 4.6847, memory: 0.0590, power: 0.1392
Test Batch 62/313, reward: 10.969, revenue_rate: 0.2841, distance: 4.7064, memory: 0.0671, power: 0.1381
Test Batch 63/313, reward: 10.697, revenue_rate: 0.2788, distance: 4.7028, memory: 0.0341, power: 0.1309
Test Batch 64/313, reward: 10.299, revenue_rate: 0.2648, distance: 4.3699, memory: 0.0455, power: 0.1249
Test Batch 65/313, reward: 11.595, revenue_rate: 0.2985, distance: 4.6306, memory: -0.0056, power: 0.1418
Test Batch 66/313, reward: 11.252, revenue_rate: 0.2939, distance: 4.7041, memory: 0.0111, power: 0.1429
Test Batch 67/313, reward: 11.253, revenue_rate: 0.2855, distance: 4.5992, memory: 0.0894, power: 0.1436
Test Batch 68/313, reward: 10.299, revenue_rate: 0.2625, distance: 4.1144, memory: 0.0754, power: 0.1244
Test Batch 69/313, reward: 10.059, revenue_rate: 0.2610, distance: 4.2582, memory: 0.0366, power: 0.1254
Test Batch 70/313, reward: 10.597, revenue_rate: 0.2723, distance: 4.2602, memory: 0.0142, power: 0.1313
Test Batch 71/313, reward: 11.075, revenue_rate: 0.2820, distance: 4.5149, memory: 0.0447, power: 0.1344
Test Batch 72/313, reward: 10.973, revenue_rate: 0.2862, distance: 4.6937, memory: 0.0871, power: 0.1475
Test Batch 73/313, reward: 10.076, revenue_rate: 0.2598, distance: 4.3121, memory: 0.0668, power: 0.1371
Test Batch 74/313, reward: 11.311, revenue_rate: 0.2940, distance: 4.8731, memory: 0.1074, power: 0.1484
Test Batch 75/313, reward: 12.567, revenue_rate: 0.3163, distance: 5.1019, memory: 0.0137, power: 0.1559
Test Batch 76/313, reward: 11.764, revenue_rate: 0.3015, distance: 4.5391, memory: -0.0342, power: 0.1427
Test Batch 77/313, reward: 11.258, revenue_rate: 0.2919, distance: 4.7947, memory: 0.0326, power: 0.1442
Test Batch 78/313, reward: 10.635, revenue_rate: 0.2737, distance: 4.3807, memory: 0.0280, power: 0.1330
Test Batch 79/313, reward: 9.916, revenue_rate: 0.2600, distance: 4.3636, memory: 0.0641, power: 0.1282
Test Batch 80/313, reward: 12.494, revenue_rate: 0.3143, distance: 4.7654, memory: 0.0051, power: 0.1484
Test Batch 81/313, reward: 10.391, revenue_rate: 0.2683, distance: 4.3583, memory: 0.0371, power: 0.1281
Test Batch 82/313, reward: 10.434, revenue_rate: 0.2722, distance: 4.4379, memory: 0.0099, power: 0.1335
Test Batch 83/313, reward: 11.300, revenue_rate: 0.2919, distance: 4.6436, memory: 0.0374, power: 0.1446
Test Batch 84/313, reward: 11.342, revenue_rate: 0.2891, distance: 4.5763, memory: 0.1004, power: 0.1462
Test Batch 85/313, reward: 11.445, revenue_rate: 0.2969, distance: 4.8488, memory: 0.0584, power: 0.1464
Test Batch 86/313, reward: 11.953, revenue_rate: 0.3023, distance: 4.5437, memory: -0.0375, power: 0.1445
Test Batch 87/313, reward: 12.057, revenue_rate: 0.3070, distance: 4.9906, memory: 0.0299, power: 0.1533
Test Batch 88/313, reward: 9.684, revenue_rate: 0.2587, distance: 4.4541, memory: 0.0810, power: 0.1342
Test Batch 89/313, reward: 10.095, revenue_rate: 0.2658, distance: 4.4459, memory: 0.0578, power: 0.1321
Test Batch 90/313, reward: 10.657, revenue_rate: 0.2778, distance: 4.4525, memory: 0.0339, power: 0.1377
Test Batch 91/313, reward: 11.151, revenue_rate: 0.2845, distance: 4.3435, memory: 0.0460, power: 0.1454
Test Batch 92/313, reward: 10.849, revenue_rate: 0.2780, distance: 4.3330, memory: 0.0225, power: 0.1286
Test Batch 93/313, reward: 12.299, revenue_rate: 0.3164, distance: 4.8151, memory: 0.0167, power: 0.1511
Test Batch 94/313, reward: 11.230, revenue_rate: 0.2910, distance: 4.6226, memory: -0.0017, power: 0.1376
Test Batch 95/313, reward: 10.662, revenue_rate: 0.2773, distance: 4.4872, memory: 0.0817, power: 0.1436
Test Batch 96/313, reward: 10.492, revenue_rate: 0.2719, distance: 4.3866, memory: 0.0111, power: 0.1298
Test Batch 97/313, reward: 11.703, revenue_rate: 0.3058, distance: 5.2505, memory: 0.1128, power: 0.1549
Test Batch 98/313, reward: 11.541, revenue_rate: 0.3010, distance: 4.9444, memory: 0.0576, power: 0.1468
Test Batch 99/313, reward: 11.535, revenue_rate: 0.2949, distance: 4.6981, memory: 0.0479, power: 0.1520
Test Batch 100/313, reward: 11.471, revenue_rate: 0.2950, distance: 5.0777, memory: 0.0948, power: 0.1486
Test Batch 101/313, reward: 12.280, revenue_rate: 0.3114, distance: 5.0178, memory: 0.0085, power: 0.1507
Test Batch 102/313, reward: 9.869, revenue_rate: 0.2605, distance: 4.2922, memory: 0.0451, power: 0.1307
Test Batch 103/313, reward: 11.207, revenue_rate: 0.2872, distance: 4.4585, memory: 0.0650, power: 0.1383
Test Batch 104/313, reward: 11.180, revenue_rate: 0.2932, distance: 4.9298, memory: 0.1207, power: 0.1513
Test Batch 105/313, reward: 11.005, revenue_rate: 0.2849, distance: 4.5161, memory: 0.0553, power: 0.1430
Test Batch 106/313, reward: 11.279, revenue_rate: 0.2871, distance: 4.3339, memory: 0.0605, power: 0.1423
Test Batch 107/313, reward: 10.838, revenue_rate: 0.2811, distance: 4.5419, memory: 0.0045, power: 0.1275
Test Batch 108/313, reward: 10.489, revenue_rate: 0.2713, distance: 4.5179, memory: 0.0383, power: 0.1350
Test Batch 109/313, reward: 10.651, revenue_rate: 0.2794, distance: 4.6424, memory: 0.0434, power: 0.1381
Test Batch 110/313, reward: 10.999, revenue_rate: 0.2826, distance: 4.4949, memory: 0.0145, power: 0.1359
Test Batch 111/313, reward: 10.084, revenue_rate: 0.2644, distance: 4.3420, memory: 0.0421, power: 0.1289
Test Batch 112/313, reward: 10.449, revenue_rate: 0.2630, distance: 4.0950, memory: 0.0034, power: 0.1228
Test Batch 113/313, reward: 11.721, revenue_rate: 0.2978, distance: 4.7964, memory: 0.0535, power: 0.1398
Test Batch 114/313, reward: 12.372, revenue_rate: 0.3173, distance: 4.7015, memory: -0.0524, power: 0.1448
Test Batch 115/313, reward: 9.821, revenue_rate: 0.2575, distance: 4.3408, memory: 0.0561, power: 0.1300
Test Batch 116/313, reward: 10.704, revenue_rate: 0.2824, distance: 4.5400, memory: 0.0475, power: 0.1418
Test Batch 117/313, reward: 11.300, revenue_rate: 0.2946, distance: 4.6507, memory: 0.0147, power: 0.1399
Test Batch 118/313, reward: 10.863, revenue_rate: 0.2828, distance: 4.7300, memory: 0.0906, power: 0.1469
Test Batch 119/313, reward: 11.376, revenue_rate: 0.2944, distance: 4.8271, memory: 0.0012, power: 0.1438
Test Batch 120/313, reward: 11.538, revenue_rate: 0.2994, distance: 4.6891, memory: 0.0306, power: 0.1478
Test Batch 121/313, reward: 9.804, revenue_rate: 0.2580, distance: 4.2631, memory: 0.0562, power: 0.1288
Test Batch 122/313, reward: 10.394, revenue_rate: 0.2686, distance: 4.2446, memory: 0.0465, power: 0.1339
Test Batch 123/313, reward: 10.802, revenue_rate: 0.2729, distance: 4.2162, memory: 0.0271, power: 0.1285
Test Batch 124/313, reward: 11.347, revenue_rate: 0.2875, distance: 4.6605, memory: 0.0189, power: 0.1388
Test Batch 125/313, reward: 10.468, revenue_rate: 0.2707, distance: 4.3160, memory: 0.0373, power: 0.1362
Test Batch 126/313, reward: 13.816, revenue_rate: 0.3505, distance: 5.3152, memory: -0.0055, power: 0.1639
Test Batch 127/313, reward: 10.819, revenue_rate: 0.2815, distance: 4.4511, memory: 0.0174, power: 0.1370
Test Batch 128/313, reward: 12.227, revenue_rate: 0.3119, distance: 4.9551, memory: 0.0196, power: 0.1455
Test Batch 129/313, reward: 12.598, revenue_rate: 0.3256, distance: 5.1427, memory: -0.0179, power: 0.1540
Test Batch 130/313, reward: 10.317, revenue_rate: 0.2688, distance: 4.3921, memory: 0.0815, power: 0.1368
Test Batch 131/313, reward: 11.730, revenue_rate: 0.3021, distance: 4.7262, memory: 0.0077, power: 0.1445
Test Batch 132/313, reward: 9.532, revenue_rate: 0.2509, distance: 4.2174, memory: 0.0881, power: 0.1310
Test Batch 133/313, reward: 10.715, revenue_rate: 0.2785, distance: 4.6551, memory: 0.0476, power: 0.1359
Test Batch 134/313, reward: 10.718, revenue_rate: 0.2818, distance: 4.6511, memory: 0.0996, power: 0.1389
Test Batch 135/313, reward: 11.096, revenue_rate: 0.2844, distance: 4.6209, memory: 0.0626, power: 0.1380
Test Batch 136/313, reward: 9.465, revenue_rate: 0.2474, distance: 3.9834, memory: 0.0810, power: 0.1188
Test Batch 137/313, reward: 10.241, revenue_rate: 0.2704, distance: 4.5242, memory: 0.1022, power: 0.1406
Test Batch 138/313, reward: 13.653, revenue_rate: 0.3525, distance: 5.4119, memory: -0.0285, power: 0.1659
Test Batch 139/313, reward: 13.035, revenue_rate: 0.3398, distance: 5.5275, memory: 0.0816, power: 0.1712
Test Batch 140/313, reward: 10.380, revenue_rate: 0.2681, distance: 4.3503, memory: 0.0270, power: 0.1255
Test Batch 141/313, reward: 11.249, revenue_rate: 0.2908, distance: 4.5625, memory: 0.0055, power: 0.1410
Test Batch 142/313, reward: 10.042, revenue_rate: 0.2592, distance: 4.2189, memory: 0.0538, power: 0.1300
Test Batch 143/313, reward: 10.258, revenue_rate: 0.2707, distance: 4.4558, memory: -0.0146, power: 0.1302
Test Batch 144/313, reward: 10.535, revenue_rate: 0.2726, distance: 4.3737, memory: 0.0199, power: 0.1310
Test Batch 145/313, reward: 13.882, revenue_rate: 0.3575, distance: 5.7928, memory: 0.0437, power: 0.1750
Test Batch 146/313, reward: 12.082, revenue_rate: 0.3076, distance: 5.0881, memory: 0.0238, power: 0.1432
Test Batch 147/313, reward: 11.057, revenue_rate: 0.2861, distance: 4.7699, memory: 0.0792, power: 0.1428
Test Batch 148/313, reward: 12.793, revenue_rate: 0.3222, distance: 4.8677, memory: 0.0171, power: 0.1539
Test Batch 149/313, reward: 9.769, revenue_rate: 0.2505, distance: 4.1179, memory: 0.0582, power: 0.1241
Test Batch 150/313, reward: 11.638, revenue_rate: 0.3047, distance: 5.0724, memory: 0.0202, power: 0.1505
Test Batch 151/313, reward: 10.855, revenue_rate: 0.2856, distance: 4.6612, memory: 0.0206, power: 0.1331
Test Batch 152/313, reward: 10.182, revenue_rate: 0.2601, distance: 4.1078, memory: 0.0392, power: 0.1270
Test Batch 153/313, reward: 10.675, revenue_rate: 0.2803, distance: 4.6929, memory: 0.0948, power: 0.1383
Test Batch 154/313, reward: 10.420, revenue_rate: 0.2737, distance: 4.4499, memory: 0.0149, power: 0.1322
Test Batch 155/313, reward: 12.570, revenue_rate: 0.3328, distance: 5.4687, memory: 0.0756, power: 0.1660
Test Batch 156/313, reward: 12.876, revenue_rate: 0.3323, distance: 5.1910, memory: 0.0176, power: 0.1581
Test Batch 157/313, reward: 10.989, revenue_rate: 0.2818, distance: 4.6610, memory: 0.0336, power: 0.1342
Test Batch 158/313, reward: 10.140, revenue_rate: 0.2602, distance: 4.2866, memory: 0.0711, power: 0.1311
Test Batch 159/313, reward: 12.711, revenue_rate: 0.3305, distance: 5.1939, memory: 0.0329, power: 0.1631
Test Batch 160/313, reward: 9.725, revenue_rate: 0.2524, distance: 4.1440, memory: 0.0821, power: 0.1243
Test Batch 161/313, reward: 9.129, revenue_rate: 0.2423, distance: 4.1585, memory: 0.1029, power: 0.1241
Test Batch 162/313, reward: 11.976, revenue_rate: 0.3031, distance: 4.5886, memory: 0.0137, power: 0.1517
Test Batch 163/313, reward: 9.896, revenue_rate: 0.2658, distance: 4.5212, memory: 0.0657, power: 0.1338
Test Batch 164/313, reward: 11.269, revenue_rate: 0.2897, distance: 4.7265, memory: 0.0461, power: 0.1453
Test Batch 165/313, reward: 10.547, revenue_rate: 0.2790, distance: 4.4574, memory: 0.0560, power: 0.1366
Test Batch 166/313, reward: 11.116, revenue_rate: 0.2818, distance: 4.4128, memory: 0.0191, power: 0.1368
Test Batch 167/313, reward: 10.565, revenue_rate: 0.2710, distance: 4.3837, memory: 0.0555, power: 0.1378
Test Batch 168/313, reward: 9.623, revenue_rate: 0.2486, distance: 3.9915, memory: 0.0205, power: 0.1237
Test Batch 169/313, reward: 11.055, revenue_rate: 0.2949, distance: 5.1706, memory: 0.0767, power: 0.1487
Test Batch 170/313, reward: 9.861, revenue_rate: 0.2579, distance: 4.1167, memory: 0.0124, power: 0.1299
Test Batch 171/313, reward: 11.159, revenue_rate: 0.2806, distance: 4.2960, memory: -0.0172, power: 0.1336
Test Batch 172/313, reward: 10.863, revenue_rate: 0.2796, distance: 4.3676, memory: 0.0502, power: 0.1384
Test Batch 173/313, reward: 10.039, revenue_rate: 0.2614, distance: 4.1361, memory: 0.0543, power: 0.1318
Test Batch 174/313, reward: 10.434, revenue_rate: 0.2740, distance: 4.4545, memory: 0.0520, power: 0.1324
Test Batch 175/313, reward: 12.245, revenue_rate: 0.3154, distance: 4.9948, memory: 0.0718, power: 0.1556
Test Batch 176/313, reward: 12.893, revenue_rate: 0.3295, distance: 4.9647, memory: 0.0168, power: 0.1579
Test Batch 177/313, reward: 13.854, revenue_rate: 0.3601, distance: 5.8859, memory: 0.0504, power: 0.1804
Test Batch 178/313, reward: 11.319, revenue_rate: 0.2942, distance: 4.8753, memory: 0.0337, power: 0.1461
Test Batch 179/313, reward: 11.668, revenue_rate: 0.2975, distance: 4.9154, memory: 0.0326, power: 0.1476
Test Batch 180/313, reward: 11.847, revenue_rate: 0.3090, distance: 5.0646, memory: 0.0263, power: 0.1455
Test Batch 181/313, reward: 10.807, revenue_rate: 0.2780, distance: 4.4741, memory: 0.0710, power: 0.1277
Test Batch 182/313, reward: 11.093, revenue_rate: 0.2775, distance: 4.2621, memory: -0.0353, power: 0.1305
Test Batch 183/313, reward: 10.465, revenue_rate: 0.2743, distance: 4.6718, memory: 0.0889, power: 0.1415
Test Batch 184/313, reward: 10.971, revenue_rate: 0.2792, distance: 4.3464, memory: 0.0083, power: 0.1345
Test Batch 185/313, reward: 11.628, revenue_rate: 0.3019, distance: 4.9410, memory: 0.0536, power: 0.1488
Test Batch 186/313, reward: 11.458, revenue_rate: 0.2956, distance: 4.7941, memory: 0.0478, power: 0.1431
Test Batch 187/313, reward: 11.592, revenue_rate: 0.3009, distance: 4.9821, memory: 0.0227, power: 0.1491
Test Batch 188/313, reward: 11.262, revenue_rate: 0.2858, distance: 4.2403, memory: 0.0213, power: 0.1323
Test Batch 189/313, reward: 11.794, revenue_rate: 0.3084, distance: 5.0895, memory: 0.0747, power: 0.1484
Test Batch 190/313, reward: 10.781, revenue_rate: 0.2776, distance: 4.4464, memory: 0.0446, power: 0.1340
Test Batch 191/313, reward: 11.639, revenue_rate: 0.3066, distance: 5.1747, memory: 0.0343, power: 0.1500
Test Batch 192/313, reward: 10.215, revenue_rate: 0.2669, distance: 4.2528, memory: 0.0253, power: 0.1353
Test Batch 193/313, reward: 10.731, revenue_rate: 0.2765, distance: 4.4366, memory: 0.0427, power: 0.1344
Test Batch 194/313, reward: 10.180, revenue_rate: 0.2634, distance: 4.2390, memory: 0.0346, power: 0.1284
Test Batch 195/313, reward: 9.948, revenue_rate: 0.2534, distance: 4.0092, memory: 0.0319, power: 0.1254
Test Batch 196/313, reward: 11.006, revenue_rate: 0.2884, distance: 4.5375, memory: 0.0260, power: 0.1439
Test Batch 197/313, reward: 10.646, revenue_rate: 0.2694, distance: 4.3240, memory: 0.0351, power: 0.1306
Test Batch 198/313, reward: 11.155, revenue_rate: 0.2859, distance: 4.3576, memory: 0.0147, power: 0.1308
Test Batch 199/313, reward: 11.332, revenue_rate: 0.2914, distance: 4.5665, memory: 0.0129, power: 0.1431
Test Batch 200/313, reward: 14.164, revenue_rate: 0.3679, distance: 5.9722, memory: 0.0524, power: 0.1852
Test Batch 201/313, reward: 12.910, revenue_rate: 0.3272, distance: 5.3058, memory: 0.0780, power: 0.1594
Test Batch 202/313, reward: 10.579, revenue_rate: 0.2783, distance: 4.6825, memory: 0.0762, power: 0.1396
Test Batch 203/313, reward: 12.846, revenue_rate: 0.3325, distance: 5.5705, memory: 0.0505, power: 0.1620
Test Batch 204/313, reward: 10.168, revenue_rate: 0.2682, distance: 4.3559, memory: 0.0176, power: 0.1334
Test Batch 205/313, reward: 11.467, revenue_rate: 0.3025, distance: 4.9817, memory: 0.0617, power: 0.1467
Test Batch 206/313, reward: 11.802, revenue_rate: 0.3028, distance: 4.7729, memory: -0.0039, power: 0.1459
Test Batch 207/313, reward: 11.866, revenue_rate: 0.3094, distance: 5.2066, memory: 0.0316, power: 0.1544
Test Batch 208/313, reward: 10.723, revenue_rate: 0.2782, distance: 4.6063, memory: -0.0096, power: 0.1313
Test Batch 209/313, reward: 9.232, revenue_rate: 0.2405, distance: 4.0730, memory: 0.0770, power: 0.1241
Test Batch 210/313, reward: 10.821, revenue_rate: 0.2839, distance: 4.9021, memory: 0.0690, power: 0.1434
Test Batch 211/313, reward: 12.568, revenue_rate: 0.3218, distance: 5.0929, memory: 0.0506, power: 0.1518
Test Batch 212/313, reward: 10.963, revenue_rate: 0.2835, distance: 4.4395, memory: 0.0264, power: 0.1336
Test Batch 213/313, reward: 12.173, revenue_rate: 0.3099, distance: 4.6470, memory: 0.0198, power: 0.1500
Test Batch 214/313, reward: 10.159, revenue_rate: 0.2634, distance: 4.1749, memory: 0.0168, power: 0.1215
Test Batch 215/313, reward: 10.808, revenue_rate: 0.2799, distance: 4.2019, memory: -0.0022, power: 0.1339
Test Batch 216/313, reward: 10.599, revenue_rate: 0.2723, distance: 4.3385, memory: 0.0264, power: 0.1307
Test Batch 217/313, reward: 10.758, revenue_rate: 0.2799, distance: 4.4960, memory: 0.0634, power: 0.1369
Test Batch 218/313, reward: 10.194, revenue_rate: 0.2655, distance: 4.5456, memory: 0.0948, power: 0.1335
Test Batch 219/313, reward: 11.530, revenue_rate: 0.3038, distance: 5.1191, memory: 0.0741, power: 0.1603
Test Batch 220/313, reward: 11.752, revenue_rate: 0.3051, distance: 4.6873, memory: -0.0146, power: 0.1414
Test Batch 221/313, reward: 11.007, revenue_rate: 0.2785, distance: 4.4198, memory: 0.0502, power: 0.1357
Test Batch 222/313, reward: 10.055, revenue_rate: 0.2593, distance: 4.2346, memory: 0.0168, power: 0.1256
Test Batch 223/313, reward: 11.181, revenue_rate: 0.2868, distance: 4.6341, memory: 0.0148, power: 0.1393
Test Batch 224/313, reward: 10.535, revenue_rate: 0.2687, distance: 4.2984, memory: 0.0287, power: 0.1342
Test Batch 225/313, reward: 11.173, revenue_rate: 0.2873, distance: 4.5111, memory: 0.0459, power: 0.1411
Test Batch 226/313, reward: 10.834, revenue_rate: 0.2811, distance: 4.6320, memory: 0.0745, power: 0.1438
Test Batch 227/313, reward: 10.599, revenue_rate: 0.2667, distance: 4.1693, memory: -0.0135, power: 0.1211
Test Batch 228/313, reward: 11.510, revenue_rate: 0.2953, distance: 4.7011, memory: 0.0407, power: 0.1382
Test Batch 229/313, reward: 10.500, revenue_rate: 0.2765, distance: 4.2544, memory: 0.0322, power: 0.1322
Test Batch 230/313, reward: 10.590, revenue_rate: 0.2792, distance: 4.5430, memory: 0.0600, power: 0.1413
Test Batch 231/313, reward: 11.973, revenue_rate: 0.3086, distance: 4.9726, memory: 0.0551, power: 0.1567
Test Batch 232/313, reward: 10.206, revenue_rate: 0.2649, distance: 4.2301, memory: 0.0434, power: 0.1268
Test Batch 233/313, reward: 10.463, revenue_rate: 0.2770, distance: 4.7059, memory: 0.0543, power: 0.1386
Test Batch 234/313, reward: 9.609, revenue_rate: 0.2504, distance: 4.0311, memory: 0.0333, power: 0.1230
Test Batch 235/313, reward: 10.240, revenue_rate: 0.2668, distance: 4.0962, memory: 0.0316, power: 0.1288
Test Batch 236/313, reward: 10.306, revenue_rate: 0.2717, distance: 4.5867, memory: 0.0372, power: 0.1328
Test Batch 237/313, reward: 11.679, revenue_rate: 0.3036, distance: 4.8859, memory: 0.0288, power: 0.1515
Test Batch 238/313, reward: 11.369, revenue_rate: 0.2916, distance: 4.5313, memory: 0.0093, power: 0.1403
Test Batch 239/313, reward: 12.331, revenue_rate: 0.3151, distance: 4.9142, memory: 0.0374, power: 0.1546
Test Batch 240/313, reward: 11.626, revenue_rate: 0.2979, distance: 4.8194, memory: 0.0683, power: 0.1476
Test Batch 241/313, reward: 10.950, revenue_rate: 0.2829, distance: 4.5314, memory: 0.0397, power: 0.1355
Test Batch 242/313, reward: 13.360, revenue_rate: 0.3399, distance: 5.2777, memory: 0.0093, power: 0.1618
Test Batch 243/313, reward: 10.399, revenue_rate: 0.2699, distance: 4.5670, memory: 0.0415, power: 0.1366
Test Batch 244/313, reward: 11.110, revenue_rate: 0.2842, distance: 4.4209, memory: 0.0043, power: 0.1359
Test Batch 245/313, reward: 10.171, revenue_rate: 0.2603, distance: 4.1362, memory: 0.0140, power: 0.1260
Test Batch 246/313, reward: 11.932, revenue_rate: 0.3092, distance: 4.8370, memory: 0.0321, power: 0.1482
Test Batch 247/313, reward: 10.558, revenue_rate: 0.2705, distance: 4.3509, memory: 0.0376, power: 0.1325
Test Batch 248/313, reward: 11.495, revenue_rate: 0.3006, distance: 5.0322, memory: 0.0662, power: 0.1483
Test Batch 249/313, reward: 10.941, revenue_rate: 0.2837, distance: 4.7599, memory: 0.0136, power: 0.1438
Test Batch 250/313, reward: 11.267, revenue_rate: 0.2965, distance: 4.9268, memory: 0.0679, power: 0.1520
Test Batch 251/313, reward: 10.502, revenue_rate: 0.2718, distance: 4.3397, memory: -0.0222, power: 0.1228
Test Batch 252/313, reward: 10.761, revenue_rate: 0.2774, distance: 4.4206, memory: 0.0102, power: 0.1284
Test Batch 253/313, reward: 10.709, revenue_rate: 0.2790, distance: 4.2183, memory: -0.0378, power: 0.1274
Test Batch 254/313, reward: 11.492, revenue_rate: 0.2974, distance: 4.7702, memory: -0.0331, power: 0.1483
Test Batch 255/313, reward: 10.575, revenue_rate: 0.2727, distance: 4.5587, memory: 0.0283, power: 0.1324
Test Batch 256/313, reward: 10.541, revenue_rate: 0.2711, distance: 4.4119, memory: 0.0695, power: 0.1356
Test Batch 257/313, reward: 11.855, revenue_rate: 0.3034, distance: 4.8224, memory: 0.0191, power: 0.1483
Test Batch 258/313, reward: 12.362, revenue_rate: 0.3182, distance: 5.3103, memory: 0.0381, power: 0.1519
Test Batch 259/313, reward: 11.264, revenue_rate: 0.2864, distance: 4.5228, memory: -0.0182, power: 0.1360
Test Batch 260/313, reward: 12.010, revenue_rate: 0.2999, distance: 4.4946, memory: 0.0219, power: 0.1440
Test Batch 261/313, reward: 10.693, revenue_rate: 0.2787, distance: 4.7785, memory: 0.0380, power: 0.1403
Test Batch 262/313, reward: 10.582, revenue_rate: 0.2745, distance: 4.4270, memory: 0.0441, power: 0.1345
Test Batch 263/313, reward: 11.642, revenue_rate: 0.2950, distance: 4.4886, memory: 0.0471, power: 0.1436
Test Batch 264/313, reward: 9.861, revenue_rate: 0.2571, distance: 4.0952, memory: 0.0298, power: 0.1280
Test Batch 265/313, reward: 10.790, revenue_rate: 0.2815, distance: 4.5842, memory: 0.0309, power: 0.1367
Test Batch 266/313, reward: 10.353, revenue_rate: 0.2693, distance: 4.2177, memory: 0.0661, power: 0.1347
Test Batch 267/313, reward: 10.644, revenue_rate: 0.2784, distance: 4.2701, memory: 0.0106, power: 0.1304
Test Batch 268/313, reward: 11.327, revenue_rate: 0.2880, distance: 4.5168, memory: 0.0339, power: 0.1383
Test Batch 269/313, reward: 10.736, revenue_rate: 0.2725, distance: 4.1349, memory: -0.0184, power: 0.1303
Test Batch 270/313, reward: 10.605, revenue_rate: 0.2764, distance: 4.5314, memory: 0.0144, power: 0.1386
Test Batch 271/313, reward: 12.091, revenue_rate: 0.3101, distance: 4.7760, memory: 0.0352, power: 0.1504
Test Batch 272/313, reward: 11.713, revenue_rate: 0.3073, distance: 5.0576, memory: 0.0884, power: 0.1535
Test Batch 273/313, reward: 11.109, revenue_rate: 0.2829, distance: 4.3968, memory: 0.0500, power: 0.1361
Test Batch 274/313, reward: 11.677, revenue_rate: 0.3061, distance: 5.0221, memory: 0.0545, power: 0.1502
Test Batch 275/313, reward: 12.120, revenue_rate: 0.3106, distance: 4.9618, memory: 0.0381, power: 0.1543
Test Batch 276/313, reward: 12.186, revenue_rate: 0.3148, distance: 5.0230, memory: 0.0180, power: 0.1437
Test Batch 277/313, reward: 10.492, revenue_rate: 0.2701, distance: 4.3045, memory: 0.0175, power: 0.1313
Test Batch 278/313, reward: 10.703, revenue_rate: 0.2713, distance: 4.4616, memory: 0.0554, power: 0.1340
Test Batch 279/313, reward: 10.314, revenue_rate: 0.2652, distance: 4.3295, memory: 0.0364, power: 0.1301
Test Batch 280/313, reward: 10.988, revenue_rate: 0.2841, distance: 4.4580, memory: -0.0055, power: 0.1339
Test Batch 281/313, reward: 12.608, revenue_rate: 0.3202, distance: 4.8625, memory: 0.0551, power: 0.1538
Test Batch 282/313, reward: 10.407, revenue_rate: 0.2693, distance: 4.4442, memory: 0.0763, power: 0.1348
Test Batch 283/313, reward: 11.506, revenue_rate: 0.3027, distance: 4.6627, memory: -0.0149, power: 0.1410
