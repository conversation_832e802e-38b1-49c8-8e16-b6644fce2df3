#!/usr/bin/env python3
"""
测试修复效果的验证脚本
验证MAML重规划器、学习率配置、训练流程集成等修复是否生效
"""

import torch
import numpy as np
import logging
from hyperparameter import parser
from constellation_smp.gpn_constellation import GPNConstellation
from replanning.maml_replanner import ConstellationMAMLReplanner
from replanning.constellation_replanning_engine import ConstellationReplanningEngine

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_hyperparameter_fixes():
    """测试超参数修复"""
    print("=" * 50)
    print("测试1: 超参数配置修复")
    print("=" * 50)
    
    args = parser.parse_args([])
    
    # 检查学习率修复
    print(f"主模型学习率 (actor_lr): {args.actor_lr}")
    print(f"MAML内循环学习率 (maml_inner_lr): {args.maml_inner_lr}")
    print(f"MAML外循环学习率 (maml_outer_lr): {args.maml_outer_lr}")
    print(f"梯度裁剪阈值 (max_grad_norm): {args.max_grad_norm}")
    print(f"适应步数 (adaptation_steps): {args.adaptation_steps}")
    print(f"全局触发阈值 (global_trigger_threshold): {args.global_trigger_threshold}")
    
    # 验证学习率比例
    lr_ratio = args.maml_inner_lr / args.actor_lr
    print(f"\nMAML内循环学习率与主学习率比例: {lr_ratio:.1f}")
    
    if lr_ratio <= 5.0:  # 应该在合理范围内
        print("✅ 学习率配置修复成功")
    else:
        print("❌ 学习率配置仍有问题")
    
    return args

def test_maml_gradient_computation():
    """测试MAML梯度计算修复"""
    print("\n" + "=" * 50)
    print("测试2: MAML梯度计算修复")
    print("=" * 50)
    
    try:
        # 创建简单的测试模型
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {device}")
        
        # 创建测试模型
        model = GPNConstellation(
            static_size=9, dynamic_size=7, hidden_size=128, 
            num_satellites=3, constellation_mode='hybrid'
        ).to(device)
        
        # 创建MAML重规划器
        maml_replanner = ConstellationMAMLReplanner(model, inner_lr=1e-4, outer_lr=1e-4)
        
        # 创建测试数据
        batch_size = 4
        seq_len = 10
        static = torch.randn(batch_size, 9, seq_len).to(device)
        dynamic = torch.randn(batch_size, 7, seq_len, 3).to(device)
        
        support_data = [{
            'static': static,
            'dynamic': dynamic,
            'target_actions': None,
            'target_satellites': None
        }]
        
        print("开始MAML快速适应测试...")
        
        # 测试快速适应
        adapted_params = maml_replanner.fast_adapt(support_data, adaptation_steps=3)
        
        if adapted_params:
            print("✅ MAML快速适应成功")
            print(f"适应后参数数量: {len(adapted_params)}")
        else:
            print("❌ MAML快速适应失败")
            
        return True
        
    except Exception as e:
        print(f"❌ MAML测试失败: {e}")
        return False

def test_replanning_engine_integration():
    """测试重规划引擎集成修复"""
    print("\n" + "=" * 50)
    print("测试3: 重规划引擎集成修复")
    print("=" * 50)
    
    try:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 创建测试模型
        model = GPNConstellation(
            static_size=9, dynamic_size=7, hidden_size=128, 
            num_satellites=3, constellation_mode='hybrid'
        ).to(device)
        
        # 创建重规划引擎配置
        config = {
            'maml_inner_lr': 1e-4,
            'maml_outer_lr': 1e-4,
            'adaptation_steps': 5,
            'performance_threshold': 0.15,
            'environment_threshold': 0.2,
            'resource_threshold': 0.8,
            'emergency_threshold': 0.1,
            'temporal_threshold': 0.3,
            'global_trigger_threshold': 0.3,
            'num_satellites': 3,
            'memory_total': 0.3,
            'power_total': 5.0
        }
        
        # 创建重规划引擎
        replanning_engine = ConstellationReplanningEngine(model, config)
        
        # 测试训练模式下的重规划
        model.train()
        print("测试训练模式下的重规划...")
        
        # 创建测试状态
        batch_size = 2
        seq_len = 5
        static = torch.randn(batch_size, 9, seq_len).to(device)
        dynamic = torch.randn(batch_size, 7, seq_len, 3).to(device)
        
        current_state = {'static': static, 'dynamic': dynamic}
        
        # 执行重规划周期
        result = replanning_engine.execute_replanning_cycle(static, dynamic)
        
        if result:
            print("✅ 重规划引擎集成测试成功")
            print(f"重规划触发: {result.get('replanning_triggered', False)}")
        else:
            print("❌ 重规划引擎集成测试失败")
            
        return True
        
    except Exception as e:
        print(f"❌ 重规划引擎测试失败: {e}")
        return False

def test_loss_function_fixes():
    """测试损失函数修复"""
    print("\n" + "=" * 50)
    print("测试4: 损失函数修复")
    print("=" * 50)
    
    try:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 创建测试模型
        model = GPNConstellation(
            static_size=9, dynamic_size=7, hidden_size=128, 
            num_satellites=3, constellation_mode='hybrid'
        ).to(device)
        
        maml_replanner = ConstellationMAMLReplanner(model, inner_lr=1e-4, outer_lr=1e-4)
        
        # 创建测试数据
        batch_size = 2
        seq_len = 5
        static = torch.randn(batch_size, 9, seq_len).to(device)
        dynamic = torch.randn(batch_size, 7, seq_len, 3).to(device)
        
        batch = {
            'static': static,
            'dynamic': dynamic,
            'target_actions': None,
            'target_satellites': None
        }
        
        print("测试损失函数计算...")
        
        # 测试损失计算
        loss = maml_replanner._compute_adaptation_loss(batch)
        
        print(f"损失值: {loss.item():.6f}")
        print(f"损失需要梯度: {loss.requires_grad}")
        print(f"损失是否为NaN: {torch.isnan(loss)}")
        print(f"损失是否为Inf: {torch.isinf(loss)}")
        
        if loss.requires_grad and not torch.isnan(loss) and not torch.isinf(loss):
            print("✅ 损失函数修复成功")
        else:
            print("❌ 损失函数仍有问题")
            
        return True
        
    except Exception as e:
        print(f"❌ 损失函数测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始验证修复效果...")
    print("注意：这是一个快速验证脚本，不会进行完整训练")
    
    results = []
    
    # 测试1: 超参数修复
    try:
        args = test_hyperparameter_fixes()
        results.append(("超参数配置", True))
    except Exception as e:
        print(f"超参数测试失败: {e}")
        results.append(("超参数配置", False))
    
    # 测试2: MAML梯度计算
    try:
        success = test_maml_gradient_computation()
        results.append(("MAML梯度计算", success))
    except Exception as e:
        print(f"MAML测试异常: {e}")
        results.append(("MAML梯度计算", False))
    
    # 测试3: 重规划引擎集成
    try:
        success = test_replanning_engine_integration()
        results.append(("重规划引擎集成", success))
    except Exception as e:
        print(f"重规划引擎测试异常: {e}")
        results.append(("重规划引擎集成", False))
    
    # 测试4: 损失函数
    try:
        success = test_loss_function_fixes()
        results.append(("损失函数修复", success))
    except Exception as e:
        print(f"损失函数测试异常: {e}")
        results.append(("损失函数修复", False))
    
    # 输出总结
    print("\n" + "=" * 50)
    print("修复验证总结")
    print("=" * 50)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name:<20}: {status}")
    
    success_count = sum(1 for _, success in results if success)
    total_count = len(results)
    
    print(f"\n总体结果: {success_count}/{total_count} 项测试通过")
    
    if success_count == total_count:
        print("🎉 所有修复验证通过！可以开始训练测试。")
    else:
        print("⚠️  部分修复需要进一步检查。")

if __name__ == "__main__":
    main()
